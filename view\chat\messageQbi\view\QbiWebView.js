var QbiWebView = {
    create(answer) {
        ChatEvent.init(CommonUtil.getLocalStorage('QbiCopilotInfo'));
        if (!parent.parent.EcpController.SHOW_WEB_TOGGLE) {
            parent.parent.EcpController.doWebViewToggle();
        }
        CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID, "showHistoryIcon", {});
        parent.parent.Window.QbiCopilotWebView.setWebView(parent.parent.document.title,answer,"URL",true);
    }
}