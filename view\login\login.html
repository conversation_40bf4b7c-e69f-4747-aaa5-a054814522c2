<!doctype html>
<html>
  <head>
    <meta name="viewport" charset="utf-8" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>
    <script src="../../ecp/EcpMemberLogin.js" charset="utf-8"></script>
    <script src="../../ecp/EcpUserLogin.js" charset="utf-8"></script>
  </head>
  <body onload="Login.doLoad()">
    <script src="./login.js" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="./login.css" charset="utf-8" />
    <div id="App">
      <section class="vh-100 mt-xl-3">
        <form>
          <div class="container py-5 h-100">
            <div class="row d-flex justify-content-center align-items-center h-100">
              <div class="col-12 col-md-8 col-lg-6 col-xl-5">
                <div class="card-body p-5 text-center">
                  <img src="../../image/icon.png" width="125px" />
                </div>
                <div style="padding-left: 10%; padding-right: 10%">
                  <div class="form-outline mb-4">
                    <label class="form-label" for="account">{{T['account']}}</label>
                    <input type="text" id="account" autocomplete="off" class="form-control form-control-lg" v-bind:placeholder="T['account_desc']" v-model="account" />
                  </div>
                  <div class="form-outline mb-4">
                    <label class="form-label" for="pd">{{T['pd']}}</label>
                    <input type="password" id="pd" autocomplete="off" class="form-control form-control-lg" v-bind:placeholder="T['pd_desc']" v-model="pd" />
                  </div>
                  <div class="form-outline mb-2">
                    <label class="form-label" for="languageSelect">{{T['dropDownI18nText_Select']}}</label>
                    <select id="languageSelect" class="form-control form-control-lg" v-model="selectedLanguage" v-on:change="changeLanguage(selectedLanguage)">
                      <option v-for="(lang, index) in i18n_langArray" :key="index" :value="lang">{{ languageTextMap[lang] }}</option>
                    </select>
                  </div>
                  <br />
                  <br />
                  <button v-if="UserLogin" class="btn btn-primary btn-lg btn-block loginBtn" type="button" v-on:click="doLogin('employee')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                      <path fill-rule="evenodd" d="M1.5 1.5A.5.5 0 0 0 1 2v4.8a2.5 2.5 0 0 0 2.5 2.5h9.793l-3.347 3.346a.5.5 0 0 0 .708.708l4.2-4.2a.5.5 0 0 0 0-.708l-4-4a.5.5 0 0 0-.708.708L13.293 8.3H3.5A1.5 1.5 0 0 1 2 6.8V2a.5.5 0 0 0-.5-.5" />
                    </svg>
                    {{T['Userlogin']}}
                  </button>
                  <button v-if="MemberLogin" class="btn btn-primary btn-lg btn-block loginBtn" type="button" v-on:click="doLogin('member')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                      <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6" />
                    </svg>
                    {{T['Memberlogin']}}
                  </button>
                  <button v-if="AnonymousLogin" class="btn btn-primary btn-lg btn-block loginBtn" type="button" v-on:click="doLogin('anonymous')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                      <path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7 7 0 0 0-2.79.588l.77.771A6 6 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755q-.247.248-.517.486z" />
                      <path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829" />
                      <path d="M3.35 5.47q-.27.24-.518.487A13 13 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7 7 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12z" />
                    </svg>
                    {{T['Anonymous']}}
                  </button>
                  <br />
                  <br />
                </div>
              </div>
            </div>
          </div>
        </form>
      </section>
    </div>
  </body>
</html>
