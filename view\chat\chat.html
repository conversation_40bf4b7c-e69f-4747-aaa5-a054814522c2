<!doctype html>
<html>

<head>
  <meta name="viewport" charset="utf-8"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

  <!-- 禁用快取 -->
  <meta http-equiv="Expires" content="0" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Cache-control" content="no-cache" />
  <meta http-equiv="Cache" content="no-cache" />

  <!-- 載入必要lib -->
  <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>
  <script src="../../lib/gpt-3-encoder/browser.js" charset="utf-8" async></script>

  <script>
    document.oncontextmenu = new Function("return false");
    oncontextmenu = "return false;";
  </script>
</head>

<body onload="Chat.doLoad()">
  <!-- 主要程式邏輯 -->
  <script src="./chat.js" charset="utf-8"></script>
  <script src="./MessageController.js?v=8.0.10.b1" charset="utf-8"></script>
  <script src="./messageQbi/QbiAnswer.js?v=8.0.10.b2" charset="utf-8"></script>
  <script src="./action/ActionManager.js?v=8.0.10.b1" charset="utf-8"></script>
  <script src="./chatEvent.js?v=8.0.10.b1" charset="utf-8"></script>
  <script src="./chatCatalog.js?v=8.0.12.b2" charset="utf-8"></script>
  <link rel="stylesheet" type="text/css" href="./message/css/MessageStyle.css" charset="utf-8" />

  <script src="./demo/demo.js" charset="utf-8"></script>
  <link rel="stylesheet" type="text/css" href="./chat.css" charset="utf-8" />

  <div id="App" style="display: none">
    <div id="messageBox" class="messageBox">
      <div id="messageList" class="messageList"></div>
      <div id="ToolZone" style="display: none">
        <div id="QuickReply_Container" class="swiper">
          <div id="QuickReply"class="swiper-wrapper cardsWrapper" ></div>
        </div>
      </div>
    </div>
    <div class="chat-input">
      <div class="chat-input-row1">
        <textarea id="sendMessageBox" v-model="inputQuestion"
          v-bind:placeholder="isMicroPhoneTurn ? '' : T['sendMessage']" onkeypress="Chat.onMessageBoxPress();"
          onkeyup="Chat.onMessageBoxUp(this)" maxlength="1000" autocomplete="off" rows="1"></textarea>
        <div class="input-right">
          <span>{{getTokensCount(inputQuestion)}}/{{TOKEN_MAX}}</span>
          <span v-if="QbiCopilotInfo.isSTTEnable && isMicroSendIcon">
            <img id="SpeechToTextEndBtn" class="microPhoneIconOn" v-bind:title="T['SpeechToTextBtnHint']"
              src="../../image/mic-red.svg" v-on:click="WebSpeechRecognition($event)" v-if="isMicroPhoneTurn" />
            <img id="SpeechToTextBtn" class="microPhoneIconOff" v-bind:title="T['SpeechToTextBtnHint']"
              src="../../image/mic.svg" v-on:click="WebSpeechRecognition($event)" v-if="!isMicroPhoneTurn" />
          </span>
        </div>
      </div>
      <div class="chat-input-row2">
        <div class="chat-actions-left">
          <span class="function-button chat-actions-left-icon" v-on:click="openFunctionView" v-if="ServerConfig.functionEnable" v-bind:title="NowSelectFunction.functionName">
            <div class="function-button-container" v-bind:class="{ 'expanded': showFunctionDropdown }">
              <svg class="function-icon" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_208_2406)">
                  <circle class="s3" cx="16.5" cy="17" r="15" />
                </g>
                <path class="s1" d="M14.9735 8.83463C15.0961 8.72508 15.3035 8.69458 15.4305 8.75289C15.6098 8.83537 15.7226 8.99358 15.7411 9.15316L15.7412 9.15845L16.222 12.9352L16.2404 13.0792L16.3317 13.19L18.7387 16.1175L18.7431 16.123C18.8577 16.2586 18.8885 16.4285 18.834 16.588C18.7604 16.743 18.6741 16.8251 18.5785 16.8687C18.5729 16.8713 18.5657 16.875 18.5459 16.8793C18.5341 16.8818 18.5191 16.8838 18.4973 16.8875C18.48 16.8904 18.4518 16.8953 18.4251 16.9L14.7672 17.3939L14.6218 17.414L14.5082 17.5108L11.6503 19.9631L11.643 19.9707C11.5177 20.0827 11.3588 20.1158 11.2043 20.0581C11.0338 19.9739 10.9269 19.8209 10.9089 19.6659L10.9084 19.6597L10.4276 15.8829L10.4099 15.7407L10.3199 15.6305L7.93645 12.6933L7.92992 12.6855L7.88944 12.631C7.80814 12.5056 7.78979 12.3582 7.83744 12.2191C7.91828 12.0426 8.06279 11.9386 8.2035 11.9213L8.2096 11.9206L11.8824 11.4243L12.0304 11.4051L12.1445 11.3048L14.9689 8.83885L14.9735 8.83463Z"/>
                <path class="s2" d="M24.7956 18.8929L26.3337 17.4709C26.4796 17.3391 26.5357 17.1333 26.501 16.939C26.4663 16.7447 26.3017 16.5715 26.1137 16.5214L24.1168 15.9636L22.7331 14.3807C22.6049 14.2306 22.4048 14.1728 22.2159 14.2083C22.0195 14.2563 21.8783 14.4082 21.8297 14.6016L21.2691 16.6598L19.731 18.0818C19.5851 18.2135 19.5291 18.4194 19.5637 18.6137C19.6105 18.8157 19.7583 18.961 19.9463 19.0111L21.948 19.5891L23.3316 21.172C23.3728 21.2153 23.4018 21.2509 23.4504 21.2817C23.572 21.3587 23.7114 21.378 23.861 21.3521C24.0574 21.3041 24.1986 21.1521 24.2472 20.9588L24.7956 18.8929Z"/>
                <path class="s2" d="M18.7777 22.0169L18.5581 20.2902C18.5393 20.1279 18.4303 19.9936 18.2868 19.9276C18.1433 19.8615 17.9534 19.8956 17.8335 20.0028L16.5421 21.1305L14.8628 21.3572C14.705 21.3766 14.5743 21.4888 14.51 21.6364C14.4505 21.795 14.4847 21.9634 14.5889 22.0867L15.6788 23.4298L15.8984 25.1566C15.9172 25.3189 16.0262 25.4532 16.1697 25.5192C16.3238 25.5803 16.4876 25.5451 16.6076 25.4378L17.9144 24.3163L19.5937 24.0895C19.6412 24.081 19.678 24.0774 19.7207 24.0579C19.8276 24.0092 19.9048 23.9214 19.9572 23.8055C20.0167 23.6469 19.9825 23.4785 19.8783 23.3552L18.7777 22.0169Z"/>
              </svg>
              <div class="function-text-container">
                <span class="function-text">{{NowSelectFunction.functionName}}</span>
              </div>
            </div>
          </span>
          <!-- 自定義功能選單 -->
          <div class="function-dropdown dispearOutline" v-show="showFunctionDropdown">
            <div class="function-menu-items">
              <!-- 清除記憶選項 -->
              <div class="function-option" v-on:click.stop="IS_CLEAR_TASK = !IS_CLEAR_TASK">
                <div class="function-option-icon">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="8" :fill="IS_CLEAR_TASK ? '#1890FF' : 'transparent'" stroke="#1890FF" stroke-width="2"/>
                    <path v-if="IS_CLEAR_TASK" d="M7 10L9 12L13 8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <span class="function-option-text">切換任務時清除記憶</span>
              </div>
              <!-- 功能選項 -->
              <div class="function-option" v-for="(item, index) in FunctionItems" :key="index" v-on:click.stop="choseFunction(item)">
                <span class="function-option-text">{{item.functionName}}</span>
              </div>
            </div>
          </div>
          <span class="km-select-button chat-actions-left-icon" v-on:click="openKMSelectView" v-bind:title="NowCatalog.FName">
            <div class="km-select-button-container" v-bind:class="{ 'expanded': showKMSelectDropdown }">
              <svg class="km-select-icon" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_208_2406)">
                  <circle class="s2" cx="15.8" cy="17" r="15"/>
                </g>
                <path class="s1" d="M9.12,7.51c.99.09,2.27.4,3.53.85,1.26.44,2.45,1.01,3.29,1.57l.25.17c.25.15.54.21.8.21s.56-.06.8-.22l.17-.12c.85-.57,2.05-1.14,3.31-1.59,1.25-.45,2.52-.77,3.51-.86h.04c.87-.07,1.62.6,1.62,1.63v12.91c0,.82-.62,1.54-1.32,1.62l-.33.05c-2.24.33-5.67,1.52-7.64,2.7l-.19.06c-.08,0-.18-.03-.22-.05l-.1-.05c-1.97-1.15-5.33-2.34-7.54-2.66l-.28-.05c-.67-.08-1.3-.81-1.3-1.62V9.01c.09-.91.81-1.52,1.62-1.45Z" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                <line class="s1" x1="17.01" y1="10.32" x2="17.01" y2="26.44" fill="none" stroke-miterlimit="10"/>
              </svg>
              <div class="km-select-text-container">
                <span class="km-select-text">{{NowCatalog.FName}}</span>
              </div>
            </div>
          </span>
          <!-- 自定義知識目錄選單 -->
          <div class="km-select-dropdown dispearOutline" v-show="showKMSelectDropdown">
            <div class="km-select-menu-items">
              <!-- 麵包屑導航 -->
              <div class="km-breadcrumb-arrow" v-if="CatalogSelects.length > 0">
                <template v-for="(item, index) in CatalogSelects">
                  <span
                    class="breadcrumb-title"
                    v-on:click.stop="doCataLogSelect(item)"
                  >
                    {{ item.FName}}
                  </span>
                  <svg
                    v-if="index < CatalogSelects.length - 1"
                    class="breadcrumb-arrow chat-actions-left-km-icon"
                    width="12"
                    height="12"
                    viewBox="0 0 18 18"
                    style="vertical-align: middle"
                  >
                    <polygon points="6,3 14,9 6,15" fill="#222" />
                  </svg>
                </template>
              </div>
              
              <!-- 第三層限制提示 -->
              <div class="km-content" v-if="CatalogSelects.length > 3">
                <div class="km-message">最多只能看到第三層目錄喔</div>
              </div>
              
              <!-- 無子目錄提示 -->
              <div class="km-content" v-else-if="!CatalogItems || CatalogItems.length === 0">
                <div class="km-message">無子目錄</div>
              </div>
              
              <!-- 目錄選項 -->
              <div class="km-content" v-else>
                <div class="km-option" v-for="(item, index) in CatalogItems" :key="index" v-on:click.stop="choseCatalog(item)" v-if="item.FId !== 'all'">
                  <span class="km-option-text">{{item.FName | truncate }}</span>
                  <svg v-if="item.hasChild && CatalogSelects.length < 2" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 4L10 8L6 12" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-actions-right">
          <div class="knowledge-mode-container" v-bind:title="T['conversationModeText']">
            <div class="knowledge-mode-dropdown dropup" v-show="showKnowledgeModeDropdown">
              <div class="knowledge-mode-menu-items">
                <div  class="knowledge-mode-option" 
                  v-bind:class="{ 'selected': knowledgeMode === 'gpt' }"
                  v-if="usermode!=='anonymous' && ServerConfig.isCopilotPassGPT" 
                  v-on:click="selectKnowledgeMode('gpt')">
                  <svg width="47" height="20" viewBox="0 0 47 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M5.06243 15.3396H4.71422C1.9285 15.3396 0.535645 14.6452 0.535645 11.1735V7.70179C0.535645 4.9244 1.9285 3.53571 4.71422 3.53571H10.2856C13.0714 3.53571 14.4642 4.9244 14.4642 7.70179V11.1735C14.4642 13.9509 13.0714 15.3396 10.2856 15.3396H9.93743C9.72154 15.3396 9.51261 15.4437 9.38029 15.6173L8.33564 17.006C7.876 17.617 7.12386 17.617 6.66422 17.006L5.61957 15.6173C5.50814 15.4646 5.25047 15.3396 5.06243 15.3396Z"
                      stroke="black" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                    <ellipse cx="4.16674" cy="8.80952" rx="0.952381" ry="0.952381" fill="black" />
                    <ellipse cx="7.49999" cy="8.80952" rx="0.952381" ry="0.952381" fill="black" />
                    <ellipse cx="10.8332" cy="8.80952" rx="0.952381" ry="0.952381" fill="black" />
                    <path
                      d="M23.3457 15.2324C22.6621 15.2324 22.0446 15.1117 21.4932 14.8701C20.9463 14.6286 20.4769 14.2822 20.085 13.8311C19.6976 13.3799 19.3991 12.8376 19.1895 12.2041C18.9798 11.5706 18.875 10.8597 18.875 10.0713V10.0576C18.875 9.01855 19.0596 8.11393 19.4287 7.34375C19.7979 6.56901 20.3174 5.96973 20.9873 5.5459C21.6618 5.11751 22.4479 4.90332 23.3457 4.90332C24.0749 4.90332 24.7243 5.0332 25.2939 5.29297C25.8682 5.54818 26.3398 5.9082 26.709 6.37305C27.0827 6.83789 27.3333 7.38249 27.4609 8.00684L27.4746 8.0752H26.2305L26.21 8.00684C26.0094 7.36882 25.6654 6.88118 25.1777 6.54395C24.6901 6.20671 24.0794 6.03809 23.3457 6.03809C22.694 6.03809 22.1266 6.20443 21.6436 6.53711C21.165 6.86523 20.7936 7.33008 20.5293 7.93164C20.265 8.5332 20.1328 9.24186 20.1328 10.0576V10.0713C20.1328 10.6911 20.2057 11.2493 20.3516 11.7461C20.502 12.2428 20.7161 12.6667 20.9941 13.0176C21.2767 13.3639 21.6162 13.6305 22.0127 13.8174C22.4092 14.0042 22.8558 14.0977 23.3525 14.0977C23.9404 14.0977 24.4554 13.9814 24.8975 13.749C25.3441 13.512 25.6904 13.1839 25.9365 12.7646C26.1826 12.3454 26.3057 11.8577 26.3057 11.3018V11.0625H23.5371V9.98242H27.5361V11.1855C27.5361 11.7917 27.4359 12.3431 27.2354 12.8398C27.0394 13.3366 26.7568 13.765 26.3877 14.125C26.0186 14.4805 25.5765 14.7539 25.0615 14.9453C24.5465 15.1367 23.9746 15.2324 23.3457 15.2324ZM30.332 11.4795V10.3857H33.1211C33.8275 10.3857 34.3743 10.2035 34.7617 9.83887C35.1491 9.46973 35.3428 8.96159 35.3428 8.31445V8.30078C35.3428 7.64909 35.1491 7.14095 34.7617 6.77637C34.3743 6.41178 33.8275 6.22949 33.1211 6.22949H30.332V5.13574H33.4355C34.0553 5.13574 34.6022 5.2679 35.0762 5.53223C35.5501 5.79655 35.9215 6.16569 36.1904 6.63965C36.4639 7.11361 36.6006 7.66276 36.6006 8.28711V8.30078C36.6006 8.92513 36.4639 9.47656 36.1904 9.95508C35.9215 10.4336 35.5501 10.8073 35.0762 11.0762C34.6022 11.3451 34.0553 11.4795 33.4355 11.4795H30.332ZM29.7168 15V5.13574H30.9473V15H29.7168ZM41.1807 15V6.24316H38.002V5.13574H45.5898V6.24316H42.4111V15H41.1807Z"
                      fill="black" />
                  </svg>

                </div>
                <div class="knowledge-mode-option" v-bind:class="{ 'selected': knowledgeMode === 'knowledge' }"
                  v-on:click="selectKnowledgeMode('knowledge')">
                  <svg width="60" height="20" viewBox="0 0 60 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M5.68652 13.7607C5.68652 13.5853 5.59468 13.4225 5.44434 13.332C3.81274 12.3511 2.5 10.4477 2.5 8.47949C2.50025 5.05146 5.66838 2.3564 9.26367 3.13574V3.13477C10.8409 3.48241 12.2171 4.52491 12.9297 5.94922V5.9502C14.3743 8.83853 12.8696 11.9489 10.5801 13.3242C10.4296 13.4146 10.337 13.5774 10.3369 13.7529V14.6309C10.3369 14.6637 10.3385 14.7168 10.3389 14.7354C10.3394 14.7643 10.3401 14.7864 10.3389 14.8057C10.3384 14.8138 10.3366 14.8206 10.3359 14.8262C10.3067 14.837 10.239 14.8574 10.1074 14.8574H5.91211C5.8064 14.8584 5.73269 14.8507 5.68262 14.8438C5.68283 14.8302 5.68313 14.8149 5.68359 14.7979C5.68474 14.7554 5.68651 14.6973 5.68652 14.6387V13.7607Z"
                      stroke="#292D32" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M6 16.4286C7.30857 16.4286 8.69143 16.4286 10 16.4286" stroke="#292D32"
                      stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M26.106 13.614H30.25V14.608H26.106V13.614ZM25.658 4.458H30.712V15.56H29.648V5.452H26.68V15.714H25.658V4.458ZM21.528 5.606H22.578V8.406C22.578 9.00333 22.536 9.64267 22.452 10.324C22.3773 10.996 22.2187 11.6773 21.976 12.368C21.7427 13.0493 21.402 13.712 20.954 14.356C20.5153 14.9907 19.932 15.5647 19.204 16.078C19.1667 16.0127 19.1013 15.9287 19.008 15.826C18.9147 15.7233 18.8213 15.6207 18.728 15.518C18.6347 15.4153 18.5507 15.3407 18.476 15.294C19.1573 14.8087 19.6987 14.2813 20.1 13.712C20.5107 13.1427 20.814 12.5547 21.01 11.948C21.2153 11.332 21.3507 10.7207 21.416 10.114C21.4907 9.50733 21.528 8.93333 21.528 8.392V5.606ZM20.086 5.116H24.804V6.096H20.086V5.116ZM18.63 8.896H25.21V9.904H18.63V8.896ZM22.242 10.73C22.354 10.8327 22.508 10.9913 22.704 11.206C22.9093 11.4113 23.1333 11.6493 23.376 11.92C23.6187 12.1813 23.8613 12.4473 24.104 12.718C24.3467 12.9793 24.5613 13.2173 24.748 13.432C24.9347 13.6467 25.0747 13.8053 25.168 13.908L24.454 14.804C24.3327 14.6267 24.1787 14.4213 23.992 14.188C23.8053 13.9453 23.6 13.6887 23.376 13.418C23.152 13.138 22.928 12.8673 22.704 12.606C22.48 12.3353 22.27 12.0927 22.074 11.878C21.8873 11.654 21.7287 11.472 21.598 11.332L22.242 10.73ZM20.198 3.226L21.22 3.436C21.0893 4.06133 20.926 4.67267 20.73 5.27C20.5433 5.858 20.3287 6.41333 20.086 6.936C19.8527 7.44933 19.596 7.90667 19.316 8.308C19.26 8.24267 19.1807 8.17267 19.078 8.098C18.9753 8.02333 18.8633 7.94867 18.742 7.874C18.63 7.79933 18.5367 7.73867 18.462 7.692C18.8727 7.15067 19.2227 6.488 19.512 5.704C19.8107 4.91067 20.0393 4.08467 20.198 3.226ZM37.796 11.682H40.75V12.452H37.796V11.682ZM37.04 4.766H41.632V5.606H37.04V4.766ZM36.662 8H45.314V8.882H36.662V8ZM37.768 9.764H41.268V14.426H37.768V13.642H40.372V10.534H37.768V9.764ZM37.404 9.764H38.244V15.182H37.404V9.764ZM38.874 3.24H39.84V5.186H38.874V3.24ZM37.614 5.9L38.356 5.746C38.4867 6.054 38.594 6.39933 38.678 6.782C38.762 7.15533 38.8087 7.482 38.818 7.762L37.992 7.944C37.992 7.664 37.9547 7.33733 37.88 6.964C37.8147 6.58133 37.726 6.22667 37.614 5.9ZM43.172 4.234L43.97 3.898C44.2873 4.27133 44.5767 4.69133 44.838 5.158C45.1087 5.61533 45.3093 6.02133 45.44 6.376L44.572 6.74C44.46 6.38533 44.2733 5.97467 44.012 5.508C43.7507 5.032 43.4707 4.60733 43.172 4.234ZM40.232 5.676L41.128 5.844C41.016 6.208 40.904 6.586 40.792 6.978C40.6893 7.36067 40.5913 7.68733 40.498 7.958L39.756 7.79C39.812 7.594 39.868 7.37 39.924 7.118C39.9893 6.866 40.05 6.614 40.106 6.362C40.162 6.10067 40.204 5.872 40.232 5.676ZM41.954 3.254H42.892C42.8733 4.99 42.8827 6.572 42.92 8C42.9667 9.428 43.046 10.66 43.158 11.696C43.27 12.732 43.4287 13.5393 43.634 14.118C43.8487 14.6873 44.124 14.986 44.46 15.014C44.5813 15.0233 44.684 14.86 44.768 14.524C44.852 14.1787 44.9127 13.7167 44.95 13.138C45.0153 13.2033 45.09 13.2733 45.174 13.348C45.258 13.4227 45.342 13.4927 45.426 13.558C45.5193 13.6233 45.594 13.67 45.65 13.698C45.5567 14.342 45.4447 14.846 45.314 15.21C45.1927 15.574 45.0573 15.826 44.908 15.966C44.7587 16.106 44.6 16.176 44.432 16.176C43.984 16.148 43.6107 15.9287 43.312 15.518C43.0227 15.1167 42.7847 14.5427 42.598 13.796C42.4207 13.0493 42.2853 12.1487 42.192 11.094C42.108 10.0393 42.052 8.854 42.024 7.538C41.996 6.222 41.9727 4.794 41.954 3.254ZM44.11 9.498L44.95 9.932C44.6607 10.856 44.2827 11.7053 43.816 12.48C43.3587 13.2547 42.8407 13.9453 42.262 14.552C41.6833 15.1493 41.0627 15.6533 40.4 16.064C40.3347 15.98 40.2413 15.8773 40.12 15.756C39.9987 15.644 39.8867 15.546 39.784 15.462C40.4373 15.0793 41.044 14.5987 41.604 14.02C42.164 13.432 42.6587 12.76 43.088 12.004C43.5267 11.248 43.8673 10.4127 44.11 9.498ZM33.092 7.482H36.508V8.308H33.092V7.482ZM33.176 3.73H36.48V4.57H33.176V3.73ZM33.092 9.344H36.508V10.184H33.092V9.344ZM32.532 5.564H36.858V6.446H32.532V5.564ZM33.568 11.234H36.536V15.308H33.568V14.44H35.668V12.102H33.568V11.234ZM33.064 11.234H33.918V15.966H33.064V11.234ZM49.43 6.544H58.922V7.384H49.43V6.544ZM48.828 13.558H59.384V14.44H48.828V13.558ZM53.504 5.536H54.498V16.134H53.504V5.536ZM50.9 10.772V11.85H57.214V10.772H50.9ZM50.9 9.036V10.086H57.214V9.036H50.9ZM49.962 8.322H58.194V12.578H49.962V8.322ZM52.874 3.24H53.952V5.032H52.874V3.24ZM48.156 4.472H59.272V5.41H48.156V4.472ZM47.652 4.472H48.66V8.868C48.66 9.40933 48.6413 9.99267 48.604 10.618C48.576 11.2433 48.5107 11.8827 48.408 12.536C48.3147 13.1893 48.1747 13.824 47.988 14.44C47.8107 15.056 47.5773 15.6207 47.288 16.134C47.2227 16.078 47.1387 16.008 47.036 15.924C46.9333 15.8493 46.826 15.7747 46.714 15.7C46.6113 15.6347 46.518 15.5833 46.434 15.546C46.714 15.07 46.9333 14.5473 47.092 13.978C47.26 13.4087 47.3813 12.8253 47.456 12.228C47.54 11.6307 47.5913 11.0473 47.61 10.478C47.638 9.89933 47.652 9.36267 47.652 8.868V4.472Z"
                      fill="black" />
                  </svg>
                </div>
                <div v-if="ServerConfig.enableWebSearch" class="knowledge-mode-option" v-bind:class="{ 'selected': knowledgeMode === 'web' }"
                  v-on:click="selectKnowledgeMode('web')">
                  <svg width="48" height="20" viewBox="0 0 48 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M7.5 3C11.366 3 14.5 6.13401 14.5 10C14.5 13.866 11.366 17 7.5 17C3.63401 17 0.5 13.866 0.5 10C0.5 6.13401 3.63401 3 7.5 3Z"
                      stroke="black" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M5.35707 3.57143C3.9285 7.74286 3.9285 12.2571 5.35707 16.4286" stroke="black"
                      stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M9.64282 3.57143C11.0714 7.74286 11.0714 12.2571 9.64282 16.4286" stroke="black"
                      stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M1.07153 12.1429C5.24296 13.5714 9.75725 13.5714 13.9287 12.1429" stroke="black"
                      stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M1.07153 7.85715C5.24296 6.42858 9.75725 6.42858 13.9287 7.85715" stroke="black"
                      stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M21.3018 15L18.6152 5.13574H19.9004L21.8623 13.1543H21.9443L24.1523 5.13574H25.3965L27.6045 13.1543H27.6865L29.6484 5.13574H30.9336L28.2471 15H27.085L24.8154 7.21387H24.7334L22.4639 15H21.3018ZM34.9805 15.1299C34.2786 15.1299 33.6748 14.9749 33.1689 14.665C32.6676 14.3551 32.2803 13.9176 32.0068 13.3525C31.738 12.7829 31.6035 12.113 31.6035 11.3428V11.3359C31.6035 10.5749 31.738 9.90723 32.0068 9.33301C32.2803 8.75879 32.6654 8.3099 33.1621 7.98633C33.6589 7.66276 34.2399 7.50098 34.9053 7.50098C35.5752 7.50098 36.1494 7.65592 36.6279 7.96582C37.111 8.27572 37.4801 8.70866 37.7354 9.26465C37.9951 9.81608 38.125 10.4587 38.125 11.1924V11.6572H32.2188V10.707H37.5166L36.9287 11.5752V11.1035C36.9287 10.5247 36.8421 10.0485 36.6689 9.6748C36.4958 9.30111 36.2565 9.02311 35.9512 8.84082C35.6458 8.65397 35.2949 8.56055 34.8984 8.56055C34.502 8.56055 34.1465 8.65853 33.832 8.85449C33.5221 9.0459 33.276 9.33073 33.0938 9.70898C32.9115 10.0872 32.8203 10.5521 32.8203 11.1035V11.5752C32.8203 12.0993 32.9092 12.5482 33.0869 12.9219C33.2646 13.291 33.5176 13.5758 33.8457 13.7764C34.1738 13.9723 34.5612 14.0703 35.0078 14.0703C35.3405 14.0703 35.6299 14.0247 35.876 13.9336C36.1221 13.8424 36.3249 13.7262 36.4844 13.585C36.6439 13.4437 36.7555 13.3001 36.8193 13.1543L36.8467 13.0928H38.0361L38.0225 13.1475C37.9587 13.3981 37.8447 13.6419 37.6807 13.8789C37.5212 14.1113 37.3138 14.3232 37.0586 14.5146C36.8034 14.7015 36.5003 14.8519 36.1494 14.9658C35.8031 15.0752 35.4134 15.1299 34.9805 15.1299ZM43.6348 15.1299C43.293 15.1299 42.9762 15.0775 42.6846 14.9727C42.3929 14.8678 42.1331 14.7197 41.9053 14.5283C41.6774 14.3324 41.4883 14.0977 41.3379 13.8242H41.2285V15H40.0391V4.70508H41.2285V8.79297H41.3379C41.4746 8.52865 41.6569 8.30078 41.8848 8.10938C42.1172 7.91797 42.3815 7.76986 42.6777 7.66504C42.9785 7.55566 43.2975 7.50098 43.6348 7.50098C44.2591 7.50098 44.8014 7.6582 45.2617 7.97266C45.7266 8.28711 46.0866 8.72917 46.3418 9.29883C46.597 9.86849 46.7246 10.5384 46.7246 11.3086V11.3223C46.7246 12.0879 46.597 12.7578 46.3418 13.332C46.0866 13.9017 45.7266 14.3438 45.2617 14.6582C44.8014 14.9727 44.2591 15.1299 43.6348 15.1299ZM43.3613 14.0771C43.8125 14.0771 44.1976 13.9678 44.5166 13.749C44.8356 13.5257 45.0794 13.209 45.248 12.7988C45.4212 12.3841 45.5078 11.8919 45.5078 11.3223V11.3086C45.5078 10.7344 45.4212 10.2422 45.248 9.83203C45.0794 9.42188 44.8356 9.10742 44.5166 8.88867C44.1976 8.66536 43.8125 8.55371 43.3613 8.55371C42.9147 8.55371 42.5296 8.66536 42.2061 8.88867C41.8825 9.11198 41.6341 9.42871 41.4609 9.83887C41.2878 10.249 41.2012 10.7389 41.2012 11.3086V11.3223C41.2012 11.8874 41.2878 12.3773 41.4609 12.792C41.6341 13.2021 41.8825 13.5189 42.2061 13.7422C42.5296 13.9655 42.9147 14.0771 43.3613 14.0771Z"
                      fill="black" />
                  </svg>
                </div>
              </div>
            </div>
            <div class="knowledge-mode-btn" v-bind:class="{ 'active': showKnowledgeModeDropdown }"
              v-on:click="toggleKnowledgeModeDropdown">
              <span class="knowledge-mode-text">
                <svg v-if="knowledgeMode === 'gpt'" width="47" height="20" viewBox="0 0 47 20" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.06243 15.3396H4.71422C1.9285 15.3396 0.535645 14.6452 0.535645 11.1735V7.70179C0.535645 4.9244 1.9285 3.53571 4.71422 3.53571H10.2856C13.0714 3.53571 14.4642 4.9244 14.4642 7.70179V11.1735C14.4642 13.9509 13.0714 15.3396 10.2856 15.3396H9.93743C9.72154 15.3396 9.51261 15.4437 9.38029 15.6173L8.33564 17.006C7.876 17.617 7.12386 17.617 6.66422 17.006L5.61957 15.6173C5.50814 15.4646 5.25047 15.3396 5.06243 15.3396Z"
                    stroke="black" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                  <ellipse cx="4.16674" cy="8.80952" rx="0.952381" ry="0.952381" fill="black" />
                  <ellipse cx="7.49999" cy="8.80952" rx="0.952381" ry="0.952381" fill="black" />
                  <ellipse cx="10.8332" cy="8.80952" rx="0.952381" ry="0.952381" fill="black" />
                  <path
                    d="M23.3457 15.2324C22.6621 15.2324 22.0446 15.1117 21.4932 14.8701C20.9463 14.6286 20.4769 14.2822 20.085 13.8311C19.6976 13.3799 19.3991 12.8376 19.1895 12.2041C18.9798 11.5706 18.875 10.8597 18.875 10.0713V10.0576C18.875 9.01855 19.0596 8.11393 19.4287 7.34375C19.7979 6.56901 20.3174 5.96973 20.9873 5.5459C21.6618 5.11751 22.4479 4.90332 23.3457 4.90332C24.0749 4.90332 24.7243 5.0332 25.2939 5.29297C25.8682 5.54818 26.3398 5.9082 26.709 6.37305C27.0827 6.83789 27.3333 7.38249 27.4609 8.00684L27.4746 8.0752H26.2305L26.21 8.00684C26.0094 7.36882 25.6654 6.88118 25.1777 6.54395C24.6901 6.20671 24.0794 6.03809 23.3457 6.03809C22.694 6.03809 22.1266 6.20443 21.6436 6.53711C21.165 6.86523 20.7936 7.33008 20.5293 7.93164C20.265 8.5332 20.1328 9.24186 20.1328 10.0576V10.0713C20.1328 10.6911 20.2057 11.2493 20.3516 11.7461C20.502 12.2428 20.7161 12.6667 20.9941 13.0176C21.2767 13.3639 21.6162 13.6305 22.0127 13.8174C22.4092 14.0042 22.8558 14.0977 23.3525 14.0977C23.9404 14.0977 24.4554 13.9814 24.8975 13.749C25.3441 13.512 25.6904 13.1839 25.9365 12.7646C26.1826 12.3454 26.3057 11.8577 26.3057 11.3018V11.0625H23.5371V9.98242H27.5361V11.1855C27.5361 11.7917 27.4359 12.3431 27.2354 12.8398C27.0394 13.3366 26.7568 13.765 26.3877 14.125C26.0186 14.4805 25.5765 14.7539 25.0615 14.9453C24.5465 15.1367 23.9746 15.2324 23.3457 15.2324ZM30.332 11.4795V10.3857H33.1211C33.8275 10.3857 34.3743 10.2035 34.7617 9.83887C35.1491 9.46973 35.3428 8.96159 35.3428 8.31445V8.30078C35.3428 7.64909 35.1491 7.14095 34.7617 6.77637C34.3743 6.41178 33.8275 6.22949 33.1211 6.22949H30.332V5.13574H33.4355C34.0553 5.13574 34.6022 5.2679 35.0762 5.53223C35.5501 5.79655 35.9215 6.16569 36.1904 6.63965C36.4639 7.11361 36.6006 7.66276 36.6006 8.28711V8.30078C36.6006 8.92513 36.4639 9.47656 36.1904 9.95508C35.9215 10.4336 35.5501 10.8073 35.0762 11.0762C34.6022 11.3451 34.0553 11.4795 33.4355 11.4795H30.332ZM29.7168 15V5.13574H30.9473V15H29.7168ZM41.1807 15V6.24316H38.002V5.13574H45.5898V6.24316H42.4111V15H41.1807Z"
                    fill="black" />
                </svg>

                <svg v-if="knowledgeMode === 'knowledge'" width="60" height="20" viewBox="0 0 60 20" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.68652 13.7607C5.68652 13.5853 5.59468 13.4225 5.44434 13.332C3.81274 12.3511 2.5 10.4477 2.5 8.47949C2.50025 5.05146 5.66838 2.3564 9.26367 3.13574V3.13477C10.8409 3.48241 12.2171 4.52491 12.9297 5.94922V5.9502C14.3743 8.83853 12.8696 11.9489 10.5801 13.3242C10.4296 13.4146 10.337 13.5774 10.3369 13.7529V14.6309C10.3369 14.6637 10.3385 14.7168 10.3389 14.7354C10.3394 14.7643 10.3401 14.7864 10.3389 14.8057C10.3384 14.8138 10.3366 14.8206 10.3359 14.8262C10.3067 14.837 10.239 14.8574 10.1074 14.8574H5.91211C5.8064 14.8584 5.73269 14.8507 5.68262 14.8438C5.68283 14.8302 5.68313 14.8149 5.68359 14.7979C5.68474 14.7554 5.68651 14.6973 5.68652 14.6387V13.7607Z"
                    stroke="#292D32" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M6 16.4286C7.30857 16.4286 8.69143 16.4286 10 16.4286" stroke="#292D32"
                    stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M26.106 13.614H30.25V14.608H26.106V13.614ZM25.658 4.458H30.712V15.56H29.648V5.452H26.68V15.714H25.658V4.458ZM21.528 5.606H22.578V8.406C22.578 9.00333 22.536 9.64267 22.452 10.324C22.3773 10.996 22.2187 11.6773 21.976 12.368C21.7427 13.0493 21.402 13.712 20.954 14.356C20.5153 14.9907 19.932 15.5647 19.204 16.078C19.1667 16.0127 19.1013 15.9287 19.008 15.826C18.9147 15.7233 18.8213 15.6207 18.728 15.518C18.6347 15.4153 18.5507 15.3407 18.476 15.294C19.1573 14.8087 19.6987 14.2813 20.1 13.712C20.5107 13.1427 20.814 12.5547 21.01 11.948C21.2153 11.332 21.3507 10.7207 21.416 10.114C21.4907 9.50733 21.528 8.93333 21.528 8.392V5.606ZM20.086 5.116H24.804V6.096H20.086V5.116ZM18.63 8.896H25.21V9.904H18.63V8.896ZM22.242 10.73C22.354 10.8327 22.508 10.9913 22.704 11.206C22.9093 11.4113 23.1333 11.6493 23.376 11.92C23.6187 12.1813 23.8613 12.4473 24.104 12.718C24.3467 12.9793 24.5613 13.2173 24.748 13.432C24.9347 13.6467 25.0747 13.8053 25.168 13.908L24.454 14.804C24.3327 14.6267 24.1787 14.4213 23.992 14.188C23.8053 13.9453 23.6 13.6887 23.376 13.418C23.152 13.138 22.928 12.8673 22.704 12.606C22.48 12.3353 22.27 12.0927 22.074 11.878C21.8873 11.654 21.7287 11.472 21.598 11.332L22.242 10.73ZM20.198 3.226L21.22 3.436C21.0893 4.06133 20.926 4.67267 20.73 5.27C20.5433 5.858 20.3287 6.41333 20.086 6.936C19.8527 7.44933 19.596 7.90667 19.316 8.308C19.26 8.24267 19.1807 8.17267 19.078 8.098C18.9753 8.02333 18.8633 7.94867 18.742 7.874C18.63 7.79933 18.5367 7.73867 18.462 7.692C18.8727 7.15067 19.2227 6.488 19.512 5.704C19.8107 4.91067 20.0393 4.08467 20.198 3.226ZM37.796 11.682H40.75V12.452H37.796V11.682ZM37.04 4.766H41.632V5.606H37.04V4.766ZM36.662 8H45.314V8.882H36.662V8ZM37.768 9.764H41.268V14.426H37.768V13.642H40.372V10.534H37.768V9.764ZM37.404 9.764H38.244V15.182H37.404V9.764ZM38.874 3.24H39.84V5.186H38.874V3.24ZM37.614 5.9L38.356 5.746C38.4867 6.054 38.594 6.39933 38.678 6.782C38.762 7.15533 38.8087 7.482 38.818 7.762L37.992 7.944C37.992 7.664 37.9547 7.33733 37.88 6.964C37.8147 6.58133 37.726 6.22667 37.614 5.9ZM43.172 4.234L43.97 3.898C44.2873 4.27133 44.5767 4.69133 44.838 5.158C45.1087 5.61533 45.3093 6.02133 45.44 6.376L44.572 6.74C44.46 6.38533 44.2733 5.97467 44.012 5.508C43.7507 5.032 43.4707 4.60733 43.172 4.234ZM40.232 5.676L41.128 5.844C41.016 6.208 40.904 6.586 40.792 6.978C40.6893 7.36067 40.5913 7.68733 40.498 7.958L39.756 7.79C39.812 7.594 39.868 7.37 39.924 7.118C39.9893 6.866 40.05 6.614 40.106 6.362C40.162 6.10067 40.204 5.872 40.232 5.676ZM41.954 3.254H42.892C42.8733 4.99 42.8827 6.572 42.92 8C42.9667 9.428 43.046 10.66 43.158 11.696C43.27 12.732 43.4287 13.5393 43.634 14.118C43.8487 14.6873 44.124 14.986 44.46 15.014C44.5813 15.0233 44.684 14.86 44.768 14.524C44.852 14.1787 44.9127 13.7167 44.95 13.138C45.0153 13.2033 45.09 13.2733 45.174 13.348C45.258 13.4227 45.342 13.4927 45.426 13.558C45.5193 13.6233 45.594 13.67 45.65 13.698C45.5567 14.342 45.4447 14.846 45.314 15.21C45.1927 15.574 45.0573 15.826 44.908 15.966C44.7587 16.106 44.6 16.176 44.432 16.176C43.984 16.148 43.6107 15.9287 43.312 15.518C43.0227 15.1167 42.7847 14.5427 42.598 13.796C42.4207 13.0493 42.2853 12.1487 42.192 11.094C42.108 10.0393 42.052 8.854 42.024 7.538C41.996 6.222 41.9727 4.794 41.954 3.254ZM44.11 9.498L44.95 9.932C44.6607 10.856 44.2827 11.7053 43.816 12.48C43.3587 13.2547 42.8407 13.9453 42.262 14.552C41.6833 15.1493 41.0627 15.6533 40.4 16.064C40.3347 15.98 40.2413 15.8773 40.12 15.756C39.9987 15.644 39.8867 15.546 39.784 15.462C40.4373 15.0793 41.044 14.5987 41.604 14.02C42.164 13.432 42.6587 12.76 43.088 12.004C43.5267 11.248 43.8673 10.4127 44.11 9.498ZM33.092 7.482H36.508V8.308H33.092V7.482ZM33.176 3.73H36.48V4.57H33.176V3.73ZM33.092 9.344H36.508V10.184H33.092V9.344ZM32.532 5.564H36.858V6.446H32.532V5.564ZM33.568 11.234H36.536V15.308H33.568V14.44H35.668V12.102H33.568V11.234ZM33.064 11.234H33.918V15.966H33.064V11.234ZM49.43 6.544H58.922V7.384H49.43V6.544ZM48.828 13.558H59.384V14.44H48.828V13.558ZM53.504 5.536H54.498V16.134H53.504V5.536ZM50.9 10.772V11.85H57.214V10.772H50.9ZM50.9 9.036V10.086H57.214V9.036H50.9ZM49.962 8.322H58.194V12.578H49.962V8.322ZM52.874 3.24H53.952V5.032H52.874V3.24ZM48.156 4.472H59.272V5.41H48.156V4.472ZM47.652 4.472H48.66V8.868C48.66 9.40933 48.6413 9.99267 48.604 10.618C48.576 11.2433 48.5107 11.8827 48.408 12.536C48.3147 13.1893 48.1747 13.824 47.988 14.44C47.8107 15.056 47.5773 15.6207 47.288 16.134C47.2227 16.078 47.1387 16.008 47.036 15.924C46.9333 15.8493 46.826 15.7747 46.714 15.7C46.6113 15.6347 46.518 15.5833 46.434 15.546C46.714 15.07 46.9333 14.5473 47.092 13.978C47.26 13.4087 47.3813 12.8253 47.456 12.228C47.54 11.6307 47.5913 11.0473 47.61 10.478C47.638 9.89933 47.652 9.36267 47.652 8.868V4.472Z"
                    fill="black" />
                </svg>
                <svg v-if="knowledgeMode === 'web'" width="48" height="20" viewBox="0 0 48 20" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.5 3C11.366 3 14.5 6.13401 14.5 10C14.5 13.866 11.366 17 7.5 17C3.63401 17 0.5 13.866 0.5 10C0.5 6.13401 3.63401 3 7.5 3Z"
                    stroke="black" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M5.35707 3.57143C3.9285 7.74286 3.9285 12.2571 5.35707 16.4286" stroke="black"
                    stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M9.64282 3.57143C11.0714 7.74286 11.0714 12.2571 9.64282 16.4286" stroke="black"
                    stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M1.07153 12.1429C5.24296 13.5714 9.75725 13.5714 13.9287 12.1429" stroke="black"
                    stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M1.07153 7.85715C5.24296 6.42858 9.75725 6.42858 13.9287 7.85715" stroke="black"
                    stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M21.3018 15L18.6152 5.13574H19.9004L21.8623 13.1543H21.9443L24.1523 5.13574H25.3965L27.6045 13.1543H27.6865L29.6484 5.13574H30.9336L28.2471 15H27.085L24.8154 7.21387H24.7334L22.4639 15H21.3018ZM34.9805 15.1299C34.2786 15.1299 33.6748 14.9749 33.1689 14.665C32.6676 14.3551 32.2803 13.9176 32.0068 13.3525C31.738 12.7829 31.6035 12.113 31.6035 11.3428V11.3359C31.6035 10.5749 31.738 9.90723 32.0068 9.33301C32.2803 8.75879 32.6654 8.3099 33.1621 7.98633C33.6589 7.66276 34.2399 7.50098 34.9053 7.50098C35.5752 7.50098 36.1494 7.65592 36.6279 7.96582C37.111 8.27572 37.4801 8.70866 37.7354 9.26465C37.9951 9.81608 38.125 10.4587 38.125 11.1924V11.6572H32.2188V10.707H37.5166L36.9287 11.5752V11.1035C36.9287 10.5247 36.8421 10.0485 36.6689 9.6748C36.4958 9.30111 36.2565 9.02311 35.9512 8.84082C35.6458 8.65397 35.2949 8.56055 34.8984 8.56055C34.502 8.56055 34.1465 8.65853 33.832 8.85449C33.5221 9.0459 33.276 9.33073 33.0938 9.70898C32.9115 10.0872 32.8203 10.5521 32.8203 11.1035V11.5752C32.8203 12.0993 32.9092 12.5482 33.0869 12.9219C33.2646 13.291 33.5176 13.5758 33.8457 13.7764C34.1738 13.9723 34.5612 14.0703 35.0078 14.0703C35.3405 14.0703 35.6299 14.0247 35.876 13.9336C36.1221 13.8424 36.3249 13.7262 36.4844 13.585C36.6439 13.4437 36.7555 13.3001 36.8193 13.1543L36.8467 13.0928H38.0361L38.0225 13.1475C37.9587 13.3981 37.8447 13.6419 37.6807 13.8789C37.5212 14.1113 37.3138 14.3232 37.0586 14.5146C36.8034 14.7015 36.5003 14.8519 36.1494 14.9658C35.8031 15.0752 35.4134 15.1299 34.9805 15.1299ZM43.6348 15.1299C43.293 15.1299 42.9762 15.0775 42.6846 14.9727C42.3929 14.8678 42.1331 14.7197 41.9053 14.5283C41.6774 14.3324 41.4883 14.0977 41.3379 13.8242H41.2285V15H40.0391V4.70508H41.2285V8.79297H41.3379C41.4746 8.52865 41.6569 8.30078 41.8848 8.10938C42.1172 7.91797 42.3815 7.76986 42.6777 7.66504C42.9785 7.55566 43.2975 7.50098 43.6348 7.50098C44.2591 7.50098 44.8014 7.6582 45.2617 7.97266C45.7266 8.28711 46.0866 8.72917 46.3418 9.29883C46.597 9.86849 46.7246 10.5384 46.7246 11.3086V11.3223C46.7246 12.0879 46.597 12.7578 46.3418 13.332C46.0866 13.9017 45.7266 14.3438 45.2617 14.6582C44.8014 14.9727 44.2591 15.1299 43.6348 15.1299ZM43.3613 14.0771C43.8125 14.0771 44.1976 13.9678 44.5166 13.749C44.8356 13.5257 45.0794 13.209 45.248 12.7988C45.4212 12.3841 45.5078 11.8919 45.5078 11.3223V11.3086C45.5078 10.7344 45.4212 10.2422 45.248 9.83203C45.0794 9.42188 44.8356 9.10742 44.5166 8.88867C44.1976 8.66536 43.8125 8.55371 43.3613 8.55371C42.9147 8.55371 42.5296 8.66536 42.2061 8.88867C41.8825 9.11198 41.6341 9.42871 41.4609 9.83887C41.2878 10.249 41.2012 10.7389 41.2012 11.3086V11.3223C41.2012 11.8874 41.2878 12.3773 41.4609 12.792C41.6341 13.2021 41.8825 13.5189 42.2061 13.7422C42.5296 13.9655 42.9147 14.0771 43.3613 14.0771Z"
                    fill="black" />
                </svg>
              </span>
              <span v-if="ServerConfig.isCopilotPassGPT || ServerConfig.enableWebSearch" class="dropdown-arrow">
                <img src="../../image/arrow_down.svg" style="vertical-align: inherit;" />
              </span>
            </div>
          </div>
          <span v-bind:title="T['sendIconHint']" v-bind:disabled="inputQuestion.trim().length === 0"
            v-on:click="sendMessage">
            <!-- <svg viewBox="0 0 28 28" fill="none">
              <circle cx="15" cy="15" r="15" />
              <path d="M15.2354 10L15.2354 21" stroke="white" stroke-width="2" stroke-linecap="round" />
              <path d="M19.4854 13.2426L15.2427 9.00001L11.0001 13.2426" stroke="white" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg> -->
            <img src="../../image/send.svg" />
          </span>
        </div>
      </div>
      <span class="audio-wave" id="siri-container" style="display: none"></span>
    </div>

    <!-- Modal Function任務切換-->
    <div class="modal fade" id="functionView" tabindex="-1" role="dialog" aria-labelledby="functionViewTitle"
      aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="functionViewTitle">{{T['functionTask']}}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" v-model="IS_CLEAR_TASK" />
              <label class="form-check-label" for="flexCheckChecked"> {{T['clearTaskInfo']}} </label>
            </div>
            <br />
            <div class="catalogItems">
              <button type="button" class="btn btn-light btn-sm btn-block catalogItem"
                v-for="(item, index) in FunctionItems" v-on:click="choseFunction(item)"
                v-bind:title="item.functionName">{{item.functionName}}</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal 單選知識-->
    <div class="modal fade" id="KMSelectView" tabindex="-1" role="dialog" aria-labelledby="KMSelectViewTitle"
      aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="KMSelectViewTitle">{{T['catalog']}}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb">
                <img src="../../image/position.png" width="15px" style="margin-right: 5px" />
                <li class="breadcrumb-item" v-for="(item, index) in CatalogSelects">
                  <a class="catalogSelect" v-on:click="doCataLogSelect(item)">{{ item.FName | truncate }}</a>
                </li>
              </ol>
            </nav>
            <div class="catalogItems" v-if="CatalogItems && CatalogSelects.length < 4">
              <button type="button" style="text-align: left" class="btn btn-light btn-sm btn-block catalogItem"
                v-for="(item, index) in CatalogItems" v-on:click="choseCatalog(item)" v-bind:title="item.FName"
                v-if="item.FId !== 'all'">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-folder"
                  viewBox="0 0 20 20">
                  <path
                    d="M.54 3.87.5 3a2 2 0 0 1 2-2h3.672a2 2 0 0 1 1.414.586l.828.828A2 2 0 0 0 9.828 3h3.982a2 2 0 0 1 1.992 2.181l-.637 7A2 2 0 0 1 13.174 14H2.826a2 2 0 0 1-1.991-1.819l-.637-7a2 2 0 0 1 .342-1.31zM2.19 4a1 1 0 0 0-.996 1.09l.637 7a1 1 0 0 0 .995.91h10.348a1 1 0 0 0 .995-.91l.637-7A1 1 0 0 0 13.81 4zm4.69-1.707A1 1 0 0 0 6.172 2H2.5a1 1 0 0 0-1 .981l.006.139q.323-.119.684-.12h5.396z" />
                </svg>
                {{ item.FName | truncate }}
              </button>
            </div>
            <div class="catalogItems" v-if="!CatalogItems">
              <i v-if="CatalogSelects.length>=4" style="margin: 20%">{{T['catalogNotice']}}</i>
              <i v-if="CatalogSelects.length< 4" style="margin: 30%">{{T['catalogNone']}}</i>
            </div>
          </div>
          <div class="modal-footer">
            <div class="text-right">
              <button type="button" class="btn btn-light btn-sm btn-block catalogItem catalogOK"
                v-on:click="_selectCataLog()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path
                    d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425z" />
                </svg>
                {{T['OK']}}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>