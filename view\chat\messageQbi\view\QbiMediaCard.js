var QbiMediaCard = {
  create(content) {
    var cardBtn = "";
    var viewHtml = "";
    var videoTypeAry = ["mp4"];
    var imageTypeAry = ["jpg", "jpeg", "png"];
    var contentUrlAry = content.originalContentUrl ? content.originalContentUrl.split(".") : [];
    var fileType = contentUrlAry.length > 0 ? contentUrlAry[contentUrlAry.length - 1].toLowerCase() : "";

    // 初始化下載信息數組
    content.downloadItems = [];

    // 檢查是否有多個媒體項目（支持同時包含圖片和影片）
    if (content.mediaItems && Array.isArray(content.mediaItems)) {
      // 處理多個媒體項目
      for (let i = 0; i < content.mediaItems.length; i++) {
        let mediaItem = content.mediaItems[i];
        let mediaFileType = mediaItem.originalContentUrl ? mediaItem.originalContentUrl.split(".").pop().toLowerCase() : "";
        if (mediaItem.mediaType === "image") {
          if (i === 0) {
            viewHtml =
              "<a href='" + mediaItem.originalContentUrl + "' target='_blank'>" +
              "<img class='cardImage' title='" +
              (mediaItem.mediaMode == "url" ? Text["pictureLink"] : mediaItem.name) +
              "' src='" +
              mediaItem.originalContentUrl +
              "' alt=\"" +
              (mediaItem.mediaMode == "url" ? Text["pictureLink"] : mediaItem.name) +
              '" />' +
              "</a>";
          }
          // 添加圖片下載信息到數組
          let imageDownloadItem = {
            type: 'Image',
            href: mediaItem.originalContentUrl,
            name: QbiMediaCard.getDownloadName(mediaItem.originalContentUrl, 'image')
          };
          content.downloadItems.push(imageDownloadItem);
        } else if (mediaItem.mediaType === "video") {
          if (i === 0) {
            if (mediaItem.originalContentUrl.toLowerCase().includes("https://www.youtube.com")) {
              viewHtml = "<iframe class='videoIframe' src='" + mediaItem.originalContentUrl + "'></iframe>";
            } else if (videoTypeAry.includes(mediaFileType)) {
              viewHtml = QbiMediaCard.createVideoPlayer(mediaItem.originalContentUrl, mediaItem.thumbnailUrl);
              // 添加影片下載信息到數組
              content.downloadItems.push({
                type: 'Video',
                href: mediaItem.originalContentUrl,
                name: QbiMediaCard.getDownloadName(mediaItem.originalContentUrl, 'video')
              });
            }
          } else {
            // 非第一個影片項目也添加下載信息
            if (!mediaItem.originalContentUrl.toLowerCase().includes("https://www.youtube.com") &&
              videoTypeAry.includes(mediaFileType)) {
              content.downloadItems.push({
                type: 'Video',
                href: mediaItem.originalContentUrl,
                name: QbiMediaCard.getDownloadName(mediaItem.originalContentUrl, 'video')
              });
            }
          }
        }
      }
    } else {
      // 原有的單一媒體項目邏輯（向後兼容）
      switch (content.mediaType) {
        case "image":
          viewHtml =
            "<a href='" + content.originalContentUrl + "' target='_blank'>" +
            "<img class='cardImage' title='" +
            (content.mediaMode == "url" ? Text["pictureLink"] : content.name) +
            "'src='" +
            content.originalContentUrl +
            "' alt=\"" +
            (content.mediaMode == "url" ? Text["pictureLink"] : content.name) +
            '" />' +
            "</a>";
          // 添加圖片下載信息到數組
          let singleImageDownloadItem = {
            type: 'Image',
            href: content.originalContentUrl,
            name: QbiMediaCard.getDownloadName(content.originalContentUrl, 'image')
          };
          content.downloadItems.push(singleImageDownloadItem);
          break;
        case "video":
          if (content.mediaMode == "url") {
            if (content.originalContentUrl.toLowerCase().includes("https://www.youtube.com")) {
              // YouTube 影片保持原有的 iframe 方式
              viewHtml = "<iframe class='videoIframe' src='" + content.originalContentUrl + "'></iframe>";
              // YouTube 影片不提供下載功能
            } else {
              if (videoTypeAry.includes(fileType)) {
                // 使用 QbiVideo 樣式的自定義播放器
                viewHtml = QbiMediaCard.createVideoPlayer(content.originalContentUrl, content.thumbnailUrl);
                // 添加影片下載信息到數組
                content.downloadItems.push({
                  type: 'Video',
                  href: content.originalContentUrl,
                  name: QbiMediaCard.getDownloadName(content.originalContentUrl, 'video')
                });
              } else {
                viewHtml =
                  "<div class='divCenter'>" +
                  "<a href='" +
                  content.originalContentUrl +
                  "' target='_blank'>" +
                  Text["PH_LinkToUrlPage"] +
                  "</a>" +
                  "</div>";
              }
            }
          } else {
            // 使用 QbiVideo 樣式的自定義播放器
            viewHtml = QbiMediaCard.createVideoPlayer(content.originalContentUrl, content.thumbnailUrl);
            // 添加影片下載信息到數組
            content.downloadItems.push({
              type: 'Video',
              href: content.originalContentUrl,
              name: QbiMediaCard.getDownloadName(content.originalContentUrl, 'video')
            });
          }
          break;
      }
    }

    /** 組成卡片按鈕 */
    for (var j = 0; j < content.FQACardAnswer.length; j++) {
      var QACardAnswer = content.FQACardAnswer[j];
      var name = QACardAnswer.FCode || QACardAnswer.FName;
      switch (QACardAnswer.Option) {
        case "Option":
          var card_code = encodeURIComponent(
            JSON.stringify({
              FCode: name,
              FDisplayText: QACardAnswer.FDisplayText,
              FShowText: QACardAnswer.FShowText,
            })
          );
          cardBtn +=
            `<li class='swiper_li' tabindex="0" ` +
            `onKeypress="ChatEvent.onAnswerButtonClick('Option','` +
            card_code +
            `');" onclick="ChatEvent.onAnswerButtonClick('Option','` +
            card_code +
            `');" >` +
            QACardAnswer.FShowText +
            "</li>";
          break;
        case "Url":
          cardBtn +=
            "<li class='swiper_li'>" +
            "<a tabindex=\"0\" onclick=ChatEvent.onAnswerButtonClick('Url','" +
            encodeURIComponent(name) +
            "') target='_blank'>" +
            QACardAnswer.FShowText +
            "</a>" +
            "</li>";
          break;
      }
    }

    // 設置兼容性的下載信息供 QbiAnswer.js 使用
    if (content.downloadItems && content.downloadItems.length > 0) {
      content.downloadHref = content.downloadItems[0].href;
      content.downloadName = content.downloadItems[0].name;
      content.downloadType = content.downloadItems[0].type;
    }
    var contentHtml =
      "   <div class='cardsSlide'>" +
      viewHtml +
      "   <ul class='swiper_ul'>" +
      cardBtn +
      "   </ul>" +
      "   </div>";
    return contentHtml;
  },

  /**
   * 創建自定義影片播放器，參考 QbiVideo.js 的樣式
   * @param {string} videoUrl - 影片URL
   * @param {string} thumbnailUrl - 縮圖URL
   * @returns {string} HTML字符串
   */
  createVideoPlayer(videoUrl, thumbnailUrl) {
    let posterAttr = thumbnailUrl ? `poster="${thumbnailUrl}"` : '';

    return `
      <div class="video-container">
        <div class="video-wrapper">
          <video class="video-player" preload="metadata" ${posterAttr}>
            <source src="${videoUrl}" type="video/mp4">
          </video>
          <div class="video-overlay">
            <div class="play-button">
              <img class="play-icon" src="../../image/play-video.svg" alt="播放" />
              <img class="pause-icon" src="../../image/pause-video.svg" alt="暫停" style="display: none;" />
            </div>
          </div>
          <div class="video-controls">
            <div class="control-buttons">
              <button class="fullscreen-btn" onclick="QbiVideo.toggleFullscreen(this)">
                <svg viewBox="0 0 24 24" fill="white">
                  <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  /**
   * 從媒體 URL 獲取下載檔名
   * @param {string} mediaUrl - 媒體URL
   * @param {string} mediaType - 媒體類型 ('image' 或 'video')
   * @returns {string} 檔名
   */
  getDownloadName(mediaUrl, mediaType = 'video') {
    try {
      let url = new URL(mediaUrl);
      let pathname = url.pathname;
      let filename = pathname.split('/').pop();

      // 如果沒有副檔名，根據媒體類型添加預設副檔名
      if (!filename.includes('.')) {
        if (mediaType === 'image') {
          filename += '.jpg';
        } else {
          filename += '.mp4';
        }
      }

      // 返回預設檔名
      if (mediaType === 'image') {
        return filename || 'image.jpg';
      } else {
        return filename || 'video.mp4';
      }
    } catch (e) {
      // 錯誤時返回預設檔名
      if (mediaType === 'image') {
        return 'image.jpg';
      } else {
        return 'video.mp4';
      }
    }
  },

  /**
   * 下載影片到本地
   * @param {string} videoUrl - 影片URL
   * @param {string} filename - 檔案名稱
   */
  async downloadVideo(videoUrl, filename) {
    try {
      const response = await fetch(videoUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下載影片時發生錯誤:', error);
      // 如果下載失敗，嘗試在新視窗開啟
      window.open(videoUrl, '_blank');
    }
  },

  /**
   * 下載圖片到本地
   * @param {string} imageUrl - 圖片URL
   * @param {string} filename - 檔案名稱
   */
  async downloadImage(imageUrl, filename) {
    try {
      // 創建一個 canvas 來處理圖片下載
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      // 設置跨域屬性
      img.crossOrigin = 'anonymous';

      img.onload = function () {
        // 設置 canvas 尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 繪製圖片到 canvas
        ctx.drawImage(img, 0, 0);

        // 將 canvas 轉換為 blob
        canvas.toBlob(function (blob) {
          if (blob) {
            // 創建下載連結
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.href = url;
            link.download = filename;

            // 觸發下載
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理 URL 對象
            URL.revokeObjectURL(url);
          } else {
            // 如果 blob 創建失敗，回退到直接下載
            QbiMediaCard.fallbackDownload(imageUrl, filename);
          }
        }, 'image/jpeg', 0.9);
      };

      img.onerror = function () {
        // 如果圖片載入失敗，回退到直接下載
        QbiMediaCard.fallbackDownload(imageUrl, filename);
      };

      // 開始載入圖片
      img.src = imageUrl;
    } catch (error) {
      console.error('下載圖片時發生錯誤:', error);
      // 如果發生錯誤，回退到直接下載
      QbiMediaCard.fallbackDownload(imageUrl, filename);
    }
  },

  /**
   * 回退下載方法
   * @param {string} url - 文件URL
   * @param {string} filename - 檔案名稱
   */
  fallbackDownload(url, filename) {
    try {
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('回退下載失敗:', error);
      // 最後的回退方案：在新視窗開啟
      window.open(url, '_blank');
    }
  }
};
