var Main = {
    MODE_USER: 'employee',
    MODE_MEMBER: 'member',
    TimeerCleanList: [],

    doLoad() {
        this.initApp();
        this.initIframePost();
        ThirdPartyTool.includeJS('../../lib/bootstrap/js/bootstrap.min.js');
        ThirdPartyTool.includeCSS('../../lib/bootstrap/css/bootstrap.min.css');
        try {
            if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
                this.goPage(ViewController.PAGE.CHAT);
            } else {
                this.goPage(ViewController.PAGE.LOGIN);
            }
        } catch (e) {
            console.log('[Main-doLoad] default login page.');
            this.goPage(ViewController.PAGE.LOGIN);
        }
        let soure = CommonUtil.getLocalStorage('source');
        if ('teams' == soure) {
            Main.doChangeSize('l');
        } else {
            Main.doChangeSize(Config.COPILOT_SIZE);
        }

        // 初始化字體大小設定
        setTimeout(() => {
            if (document.getElementById('mainFrame') && document.getElementById('mainFrame').contentWindow.Chat) {
                document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
            }
        }, 1000);
    },
    initIframePost() {
        CommonUtil.listen({
            AddNewChatToHistoryList: (data) => {
                HistoryPage.AddNewChatToHistoryList(data);
            },
            loadHistoryList: () => {
                HistoryPage.loadHistoryList();
            },
            handleFirstMessageInRoom: (data) => {
                HistoryPage.handleFirstMessageInRoom(data);
            },
            cleanHistoryList: () => {
                HistoryPage.cleanHistoryList();
            },
            watchChatLock: (isLock) => {
                Main.PageApp.isLock = isLock;
            },
            doGetHistoryData: (data) => {
                HistoryPage.doGetHistoryData(data.newRoomId);
            },
            cleanhistoryNowId: () => {
                Main.PageApp.historyNowId = null;
            },
            hideAccountView: () => {
                Main.hideAccountView();
            },
        });
    },
    initApp() {
        Main.PageApp = new Vue({
            el: "#App",
            data: {
                T: Text,
                showLogout: false,
                showLeave: false,
                showAutoSpeech: false,
                isLogin: false,
                version: ThirdPartyTool.Version,
                isTeamsChannel: CommonUtil.isTeamsChannel(),
                isCustomCssEnable: Config.CUSTOM_CSS_ENABLE,
                userTypeContent: Text['userName'],
                UserInfo: {
                    userName: '',
                    userIcon: '',
                    userMode: '',
                },
                progress: 0,
                progressId: '',
                pin: true,
                isMobile: parent.EcpController._isMobile(),
                AutoSpeech: false,
                size: 's',
                Fontsize: 's',
                showHistoryIcon: true,
                showHistoryPanel: false,
                historyList: [],
                categorizedHistory: {
                    '今天': [],
                    '過去 7 天': [],
                    '過去 30 天': [],
                    '月份': {},
                    '年份': {}
                },
                historyNowId: null, // 當前歷史紀錄ID
                showMenuId: null,    // 哪個項目的三點選單顯示
                editingId: null,     // 哪個項目正在編輯
                tempName: "",            // 暫存輸入框內容
                fromIndex: 0,
                toIndex: 20,
                isHistoryDataLoading: false,
                isHistoryListLoading: false,
                noMoreData: false,
                isLock: false,
            },
            directives: {
                focus: {
                    inserted: function (el) {
                        el.focus();
                    }
                }
            },
            methods: {
                doToggle(event) {
                    Main.doToggle();
                },
                doHistoryToggle(event) {
                    // 隱藏歷史紀錄圖示
                    Main.PageApp.showHistoryIcon = !Main.PageApp.showHistoryIcon;
                    Main.PageApp.showHistoryPanel = !Main.PageApp.showHistoryPanel;
                },
                openUserInfo(event) {
                    Main.toggleAccountView();
                },
                doLogout(event) {
                    Main.doLogout();
                },
                doChangeSize(size) {
                    Main.doChangeSize(size);
                },
                doTogglePin() {
                    if (!CommonUtil.isTeamsChannel()) {
                        parent.EcpController.pin = !parent.EcpController.pin;
                        Main.PageApp.pin = parent.EcpController.pin;
                    } else {
                        console.warn('[Main] is teams channel , not support toggle pin.');
                    }
                },
                switchAutoSpeech() {
                    Main.switchAutoSpeech();
                },
                switchFontSize(isdefault) {
                    Main.switchFontSize(isdefault);
                },
                cycleSizeChange() {
                    Main.cycleSizeChange();
                },
                cycleFontSizeChange() {
                    Main.cycleFontSizeChange();
                },
                toggleMoreMenu() {
                    Main.toggleMoreMenu();
                },
                doNewChat() {
                    document.getElementById('mainFrame').contentWindow.Chat.cleanMessageAndShowGreeting();
                },
                cleanMessageAndShowGreeting() {
                    document.getElementById('mainFrame').contentWindow.Chat.cleanMessageAndShowGreeting();
                },
                // 取得歷史紀錄資訊
                doGetHistoryData(roomId) {
                    if (Main.PageApp.isHistoryDataLoading || Main.PageApp.isLock || roomId === Main.PageApp.historyNowId) return;

                    // 點擊 historyItem 時關閉選單
                    if (Main.PageApp.showMenuId !== null) {
                        Main.PageApp.showMenuId = null;
                    }

                    document.getElementById('mainFrame').contentWindow.Chat.doGetHistoryData({ newRoomId: roomId });
                },
                // 切換三點選單顯示/隱藏
                toggleMenu(roomId, event) {
                    const isSameId = Main.PageApp.showMenuId === roomId;
                    Main.PageApp.showMenuId = null;
                    if (!isSameId) {
                        Main.PageApp.showMenuId = roomId;
                        // 動態設置選單位置
                        Main.PageApp.$nextTick(() => {
                            const menuElement = document.querySelector('.itemMenu');
                            if (menuElement && event && event.target) {
                                const buttonRect = event.target.closest('.dotsBtn').getBoundingClientRect();
                                const historyPanelRect = document.querySelector('#historyPanel').getBoundingClientRect();

                                // 計算選單位置
                                let top = buttonRect.bottom + 5; // 按鈕下方 5px
                                let left = buttonRect.right - 23; // 選單寬度 100px，右對齊按鈕

                                // 確保選單不會超出視窗範圍
                                const menuHeight = 80; // 預估選單高度
                                const windowHeight = window.innerHeight;
                                const windowWidth = window.innerWidth;

                                // 垂直位置調整
                                if (top + menuHeight > windowHeight) {
                                    top = buttonRect.top - menuHeight - 5; // 顯示在按鈕上方
                                }
                                menuElement.style.top = `${top}px`;
                                menuElement.style.left = `${left}px`;
                            }
                        });
                    }
                },

                // 開始重新命名
                startRename(roomId) {
                    Main.PageApp.editingId = roomId;
                    Main.PageApp.tempName = Main.PageApp.historyList.find(item => item.FId === roomId).FName;
                    Main.PageApp.showMenuId = null;
                },
                // 失焦時自動保存
                applyRename(roomId) {
                    if (Main.PageApp.editingId === null) return;

                    let item = Main.PageApp.historyList.find(i => i.FId === roomId);
                    if (!item) return this._clearEditing();

                    if (!Main.PageApp.tempName || item.FName === Main.PageApp.tempName) {
                        Main.PageApp.tempName = item.FName;
                        return this._clearEditing();
                    }
                    item.isCleanChat = false;
                    item.FName = Main.PageApp.tempName;
                    HistoryPage.doSetHistoryName(roomId, Main.PageApp.tempName, true);
                    Main.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(Main.PageApp.historyList);
                    document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = Main.PageApp.historyList;
                    this._clearEditing();
                },
                _clearEditing() {
                    Main.PageApp.editingId = null;
                    Main.PageApp.tempName = "";
                },
                // 刪除該筆紀錄
                deleteItem(roomId) {
                    const index = Main.PageApp.historyList.findIndex(i => i.FId === roomId);
                    if (index !== -1 && !Main.PageApp.isLock) {
                        Main.PageApp.historyList.splice(index, 1);
                        Main.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(Main.PageApp.historyList); // 重新分類
                        document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = Main.PageApp.historyList;
                        // 刪除流程
                        if (Main.PageApp.historyNowId === roomId) {
                            HistoryPage.doDeleteHistoryData(roomId);
                            if (Main.PageApp.historyList.length === 0) {
                                document.getElementById('mainFrame').contentWindow.Chat.cleanMessageAndShowGreeting();
                            } else {
                                Main.PageApp.historyNowId = Main.PageApp.historyList[0].FId;
                                document.getElementById('mainFrame').contentWindow.Chat.doGetHistoryData({ newRoomId: Main.PageApp.historyList[0].FId });
                            }
                        } else {
                            HistoryPage.doDeleteHistoryData(roomId);
                        }
                    }
                    Main.PageApp.showMenuId = null;
                },
                // 滑鼠離開關閉menu
                handleMouseLeave(e) {
                    // Main.PageApp.showMenuId = null;
                },
                handleParentClick(e) {
                    if (Main.PageApp.showMenuId !== null) {
                        Main.PageApp.showMenuId = null;
                    }
                },
                // 處理點擊外部關閉選單
                handleOutsideMenuClick(e) {
                    if (Main.PageApp.showMenuId !== null) {
                        const menuElement = document.querySelector('.itemMenu');
                        const dotsBtn = e.target.closest('.dotsBtn');

                        // 檢查是否點擊了選單內容或三點按鈕
                        const isMenuClick = menuElement?.contains(e.target);
                        const isDotsClick = !!dotsBtn;

                        // 如果不是點擊選單內容也不是點擊三點按鈕，則關閉選單
                        if (!isMenuClick && !isDotsClick) {
                            Main.PageApp.showMenuId = null;
                        }
                    }
                },
                handleScroll(e) {
                    const el = e.target;
                    const isBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 10;
                    console.log('handleScroll triggered:', {
                        scrollTop: el.scrollTop,
                        clientHeight: el.clientHeight,
                        scrollHeight: el.scrollHeight,
                        isBottom: isBottom,
                        isLoading: Main.PageApp.isHistoryListLoading,
                        noMoreData: Main.PageApp.noMoreData,
                        historyListLength: Main.PageApp.historyList.length
                    });
                    
                    if (isBottom && !Main.PageApp.isHistoryListLoading && !Main.PageApp.noMoreData && Main.PageApp.historyList.length > 0) {
                        console.log('Loading more history...');
                        HistoryPage.loadHistoryList();
                    }
                },
            },
            

            mounted() {
                const panel = this.$el.querySelector('.historyPanel');
                console.log('mounted - panel found:', !!panel);
                if (panel) {
                    panel.addEventListener('scroll', this.handleScroll);
                    console.log('Scroll event listener added to .historyPanel');
                } else {
                    console.error('Could not find .historyPanel element');
                }

                // 添加全域點擊事件監聽器來關閉選單
                document.addEventListener('click', this.handleOutsideMenuClick);
            },
            beforeDestroy() {
                // 清理事件監聽器
                document.removeEventListener('click', this.handleOutsideMenuClick);
            },
            beforeDestroy() {
                const panel = this.$el.querySelector('.historyPanel');
                if (panel) {
                    panel.removeEventListener('scroll', this.handleScroll);
                }
            },
            watch: {},
        });
    },

    doChangeSize(size) {
        parent.EcpController.NOW_SIZE = size;
        Main.PageApp.size = size;
        if (Main.PageApp.isMobile) {
            console.debug('[Main - doChangeSize] Mobile can not change size.');
            return;
        }
        let mainView = parent.EcpController.KM_COPILOT_ID;
        let webView = parent.EcpController.KM_COPILOT_WEB_ID;
        switch (size) {
            case 's': //default
                parent.document.getElementById(mainView).style.minWidth = '380px';
                parent.document.getElementById(mainView).style.width = 'calc(43vh - 0px)';
                parent.document.getElementById(webView).style.width = 'calc(70vh - 0px)';
                Main._resizeWeb(webView);
                break;
            case 'm':
                let width = parent.document.body.clientWidth / 2;
                let width_screen = 620;
                parent.document.getElementById(webView).style.right = width > width_screen ? '50.5%' : '625px';
                parent.document.getElementById(webView).style.width = '48%';
                parent.document.getElementById(mainView).style.width = width > width_screen ? '50%' : '620px';
                break;
            case 'l':
                parent.document.getElementById(mainView).style.width = '99%';
                parent.document.getElementById(webView).style.width = '99%';
                parent.document.getElementById(webView).style.right = '0px';
                break;
        }
    },
    doToggle() {
        parent.EcpController.doToggle();
    },

    goPage(path) {
        ViewController.setView(path);
    },

    switchAutoSpeech() {
        // 由於使用了 v-model，AutoSpeech 的值已經被自動更新了，所以這裡不需要再切換
        document.getElementById('mainFrame').contentWindow.Chat.isAutoSpeechSynthesis = Main.PageApp.AutoSpeech;
        document.getElementById('mainFrame').contentWindow.Chat.speakingCancel();
        document.getElementById('mainFrame').contentWindow.WebSpeechSynthesis.speak("  ");
    },

    toggleAccountView() {
        const accountView = document.getElementById('accountView');
        if (accountView.classList.contains('show')) {
            Main.hideAccountView();
        } else {
            Main.showAccountView();
        }
    },

    showAccountView() {
        const accountView = document.getElementById('accountView');
        accountView.classList.add('show');
        // 添加點擊外部關閉功能
        setTimeout(() => {
            document.addEventListener('click', Main.handleOutsideClick);
        }, 100);
    },

    hideAccountView() {
        const accountView = document.getElementById('accountView');
        accountView.classList.remove('show');
        document.removeEventListener('click', Main.handleOutsideClick);
    },

    handleOutsideClick(event) {
        const accountView = document.getElementById('accountView');

        // 如果點擊的不是 accountView 內部，則關閉選單
        if (!accountView.contains(event.target)) {
            Main.hideAccountView();
        }
    },


    switchFontSize(isdefault) {
        if (isdefault) {

        } else {
            switch (Main.PageApp.Fontsize) {
                case "l":
                    Main.PageApp.Fontsize = "s";
                    document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
                    break;
                case "m":
                    Main.PageApp.Fontsize = "l";
                    document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
                    break;
                case "s":
                    Main.PageApp.Fontsize = "m";
                    document.getElementById('mainFrame').contentWindow.Chat.changeFontSize(Main.PageApp.Fontsize);
                    break;
            }
        }

    },

    cycleSizeChange() {
        let currentSize = Main.PageApp.size;
        let nextSize;
        switch (currentSize) {
            case 's':
                nextSize = 'm';
                break;
            case 'm':
                nextSize = 'l';
                break;
            case 'l':
                nextSize = 's';
                break;
            default:
                nextSize = 's';
        }
        Main.doChangeSize(nextSize);
    },

    cycleFontSizeChange() {
        Main.switchFontSize(false);
    },

    toggleMoreMenu() {
        Main.PageApp.openUserInfo();
    },

    showLoading(show, animate) {
        console.debug('[Main-showLoading] show = ' + show + ' ,animate = ' + animate);
        if (show) {
            Main.PageApp.progress = 10;
            $('#loading').show(animate);
            Main.PageApp.progressId = setInterval(function () {
                if (Main.PageApp.progress >= 90) {
                    Main._cleanTimer();
                } else {
                    Main.PageApp.progress += 5;
                }
            }, 1000 * 5);
            Main.TimeerCleanList.push(Main.PageApp.progressId);
        } else {
            Main.PageApp.progress = 100;
            $('#loading').hide();
            Main._cleanTimer();
        }
    },

    doNotReady() {
        Swal.fire({
            icon: 'error',
            title: Text['errorNotReadyTitle'],
            text: Text['errorNotReadyContent'],
            showCloseButton: false,
            showCancelButton: false,
            showConfirmButton: false,
        });
        this.showLoading(false);
    },

    doLogout() {
        try {
            switch (CommonUtil.getLocalStorage('UserInfo').mode) {
                case Main.MODE_USER:
                    let tokenId = CommonUtil.getLocalStorage('UserInfo').tokenData.tokenId;;
                    EcpUserLogin.doLogout(tokenId);
                    break;
                default:
                    console.log('[Main-doLogout] Anonymous logout.');
                    break;
            }
            $('#userIcon').hide(100);
            $('#historyIcon').hide(100);
            $('#mainFontSize').hide(100);
            $('#newChatIcon').hide(100);
            $('#mainSize').hide(100);
            Main.hideAccountView();
            Main.PageApp.UserInfo.userName = '';
            Main.PageApp.UserInfo.userIcon = '';
            Main.PageApp.UserInfo.userMode = '';
            Main.goPage(ViewController.PAGE.LOGIN);
            Main.PageApp.showLogout = false;
            Main.PageApp.showLeave = false;
            Main.PageApp.AutoSpeech = false;
            Main.PageApp.isLogin = false;
            Main.PageApp.showHistoryIcon = false;
            Main.PageApp.showHistoryPanel = false;
            if (parent.EcpController.SHOW_WEB_TOGGLE) {
                parent.EcpController.doWebViewToggle();
            }

            if (document.getElementById('mainFrame').contentWindow.Chat) {
                document.getElementById('mainFrame').contentWindow.Chat.cleanMessage();
            }
            CommonUtil.deleteLocalStorage('UserInfo');
            let soure = CommonUtil.getLocalStorage('source');
            if ('teams' == soure) {
                Main.doChangeSize('l');
            } else {
                Main.doChangeSize(Config.COPILOT_SIZE);
                Main.switchFontSize(true);
            }
            setTimeout(function () {
                Main.showLoading(false);
            }, 1500);
        } catch (e) {
            console.warn('[Main - doLogout] not ready.');
            location.reload();
        }
    },

    _resizeWeb(webView) {
        if (parent.EcpController.SHOW_WEB_TOGGLE) {
            parent.$('#' + webView).hide();
        }
        setTimeout(function () {
            parent.EcpController.setWebViewPosition();
            if (parent.EcpController.SHOW_WEB_TOGGLE) {
                parent.$('#' + webView).show(250);
            }
        }, 1000);
    },

    _cleanTimer() {
        Main.TimeerCleanList.forEach(function (timer) {
            try {
                clearInterval(timer);
            } catch (e) {
                console.warn(e);
            }
        });
        Main.TimeerCleanList = [];
    }
}


var HistoryPage = {
    isFirstLoad: true,
    loadHistoryList() {
        if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot && HistoryPage.isFirstLoad) {
            HistoryPage.isFirstLoad = false;
            Config.IS_ECP_MODE = true;
            Config.ECP_URL = parent.parent.Utility.getBaseUrl();
        }
        HistoryPage._executeLoadHistoryList();
    },
    
    _executeLoadHistoryList() {
        if (CommonUtil.getLocalStorage('UserInfo').mode === 'anonymous' || Main.PageApp.isHistoryListLoading || Main.PageApp.noMoreData) return;
        Main.PageApp.isHistoryListLoading = true;
        HttpQuery.doRequest('/openapi/copilot/getAIRooms', {
            uid: CommonUtil.getLocalStorage('UserInfo').userId,
            fi: Main.PageApp.fromIndex,
            ti: Main.PageApp.toIndex,
        }, (result) => {
            const list = result.payload?.list || [];

            if (list.length === 0) {
                Main.PageApp.noMoreData = true;
            } else {
                Main.PageApp.historyList = Main.PageApp.historyList.concat(list)
                Main.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(Main.PageApp.historyList);
                document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = Main.PageApp.historyList;
                Main.PageApp.fromIndex = Main.PageApp.toIndex + 1;
                Main.PageApp.toIndex = Main.PageApp.fromIndex + 9;
            }
            setTimeout(() => {
                Main.PageApp.isHistoryListLoading = false;
            }, 500);
        });
    },
    cleanHistoryList() {
        Main.PageApp.historyList = [];
        Main.PageApp.categorizedHistory = {
            '今天': [],
            '過去 7 天': [],
            '過去 30 天': [],
            '月份': {},
            '年份': {}
        };
        Main.PageApp.fromIndex = 0;
        Main.PageApp.toIndex = 20;
        Main.PageApp.isHistoryDataLoading = false;
        Main.PageApp.isHistoryListLoading = false;
        Main.PageApp.noMoreData = false;
        Main.PageApp.historyNowId = null;
    },
    AddNewChatToHistoryList(data) {
        if (data != null) {
            Main.PageApp.historyNowId = data.roomId;
            const list = [data];
            Main.PageApp.historyList = list.concat(Main.PageApp.historyList)
            Main.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(Main.PageApp.historyList);
            document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = Main.PageApp.historyList;
        }
    },
    handleFirstMessageInRoom(data) {
        if (!!!data) return;
        let room = Main.PageApp.historyList.find(item => item.FId === data.roomId);
        if (room.FName === Text['newChat']) {
            room.isCleanChat = false;
            room.FName = data.inputQuestion;
            HistoryPage.doSetHistoryName(data.roomId, data.inputQuestion, false);
            Main.PageApp.categorizedHistory = HistoryPage.categorizeHistoryList(Main.PageApp.historyList);
            document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = Main.PageApp.historyList;
        }
    },
    categorizeHistoryList(historyList) {
        const now = new Date();
        const categories = {
            '今天': [],
            '過去 7 天': [],
            '過去 30 天': [],
            '月份': {},
            '年份': {}
        };

        historyList.forEach(item => {
            const nowDate = new Date();
            nowDate.setHours(0, 0, 0, 0);
            const targetDate = new Date(item.FCreateTime);
            targetDate.setHours(0, 0, 0, 0);
            const diffDays = Math.floor((nowDate - targetDate) / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                categories['今天'].push(item);
            } else if (diffDays <= 7) {
                categories['過去 7 天'].push(item);
            } else if (diffDays <= 30) {
                categories['過去 30 天'].push(item);
            } else if (targetDate.getFullYear() === now.getFullYear()) {
                const label = `${targetDate.getFullYear()} 年 ${targetDate.getMonth() + 1} 月`;
                if (!categories['月份'][label]) categories['月份'][label] = [];
                categories['月份'][label].push(item);
            } else {
                const label = `${targetDate.getFullYear()} 年`;
                if (!categories['年份'][label]) categories['年份'][label] = [];
                categories['年份'][label].push(item);
            }
        });

        return categories;
    },
    doGetHistoryData(roomId) {
        // 取得歷史紀錄資訊
        if (Main.PageApp.historyNowId != null) {
            const item = Main.PageApp.historyList.find(
                item => item.FId === Main.PageApp.historyNowId
            );
            if (item) {
                item.isNewStartChat = false;
            }
        }
        document.getElementById('mainFrame').contentWindow.Chat.PageApp.historyList = Main.PageApp.historyList;
        Main.PageApp.isHistoryDataLoading = true;
        Main.PageApp.historyNowId = roomId;
        HttpQuery.doRequest('/openapi/copilot/getChatHistory', {
            uid: CommonUtil.getLocalStorage('UserInfo').userId,
            rid: roomId,
            fi: 0,
            ti: 1000,
        }, function (result) {
            if (result._header_.success) {
                document.getElementById('mainFrame').contentWindow.Chat.doShowHistoryData(
                    {
                        historyData: result?.payload?.FHtmlView?.views,
                        chat_history_list: result?.payload?.FLLMChatHistory?.chat_history_list,
                        chatRoomId: roomId,
                    });
            } else {
                console.log('更新失敗');
            }
            setTimeout(() => {
                Main.PageApp.isHistoryDataLoading = false;
            }, 1000);
        });
    },
    doSetHistoryName(roomId, newName, isUserEdit) {
        HttpQuery.doRequest('/openapi/copilot/updateAIRoomName', {
            rid: roomId,
            rn: newName,
            isUserEdit: isUserEdit
        }, function (result) {
            if (result._header_.success) {
                // 更新成功
                console.log('更新成功');
            } else {
                // 更新失敗
                console.log('更新失敗');
            }
        });
    },
    doDeleteHistoryData(FId) {
        HttpQuery.doRequest('/openapi/copilot/deleteAIRoom', {
            rid: FId
        }, function (result) {
            if (result._header_.success) {
                // 刪除成功
                console.log('刪除成功');
            } else {
                // 刪除失敗
                console.log('刪除失敗');
            }
        });
    }
};  