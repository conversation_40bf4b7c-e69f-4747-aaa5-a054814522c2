/* MessageGptText */
.MessageGptText {
    display: table-cell;
    width: 95%;
}

.ViewMoreText {
    float: inline-end;
    padding: 3px;
    border-radius: 3px;
}

.hljs,
pre,
code {
    display: inline-grid;
}

.WordBreakAll {
    word-break: break-all;
}

/* GptRelated */
.GptRelated {
    border: rgb(180, 201, 232) solid;
    background-color: rgb(227, 239, 255);
    border-radius: 8px;
    padding: 5px;
    text-align: left;
    word-wrap: break-word;
    display: inline-block;
    margin: 5px;
    cursor: pointer;
    transition: background-color 0.1s;
}

.GptRelated:hover {
    background-color: rgb(255, 255, 255);
    -webkit-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}

/* MessageText */
.MessageText {
    border: none;
    background-color: #f1f1f1;
    border-radius: 10px;
    padding: 10px 12px;
    text-align: left;
    word-wrap: break-word;
    display: inline-block;
    min-width: 20px;
    max-width: 60%;
    font-size: 14px;
}

/* MessageText with Timestamp Container */
.MessageText_WithTimestamp {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    margin-bottom: 15px;
}

/* Timestamp styling */
.MessageText_Timestamp {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    align-self: flex-end;
    margin-bottom: 2px;
}

/* MessageWebSearchText */
.MessageWebSearchText {
    display: table-cell;
    width: 95%;
}


/* --------------滿意度按鈕-------------- */

.satisfactionBtnLike {
    /* width: 20px; */
    width: 14px;
    height: 14px;
    aspect-ratio: 1/1;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.satisfactionBtnOk {
    /* width: 20px; */
    width: 14px;
    height: 14px;
    aspect-ratio: 1/1;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.satisfactionBtnDislike {
    /* width: 20px; */
    width: 14px;
    height: 14px;
    aspect-ratio: 1/1;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.satisfactionBtnCopy {
    /* width: 20px; */
    width: 14px;
    height: 14px;
    aspect-ratio: 1/1;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.satisfactionBtnLike:hover {
    width: 25px;
}

.satisfactionBtnOk:hover {
    width: 25px;
}

.satisfactionBtnDislike:hover {
    width: 25px;
}

.satisfactionBtnCopy:hover {
    width: 25px;
}

.grayScaleIcon {
    filter: grayscale(100%);
}

/* --------------滿意度-------------- */

.satisfactionBar {
    position: relative;
    padding-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 10px;
}


/* --------------訊息框CSS-------------- */
.MessageText_Container {
    display: table-cell;
    width: 95%;
}

.MessageText_Satis {
    width: 100%;
    /* display: flex;
    justify-content: flex-end;
    align-items: flex-end; */
}

.ChatMessageLeft {
    text-align: left;
}

.ChatMessageLeft .MessageText {
    min-width: 300px;
}

.ChatMessageLeft .MessageText_WithTimestamp {
    flex-direction: row;
}

.ChatMessageRight {
    text-align: right;
}

.ChatMessageRight .MessageText {
    background-color: #E4F7FF;
}

.ChatMessageRight .MessageText_WithTimestamp {
    flex-direction: row-reverse;
}

.ChatMessage {
    margin: 15px 10px;
}


/* --------------語音按鈕-------------- */
.SpeakMessageVolume {
    filter: brightness(0.93);
    /* width: 20px; */
    width: 14px;
    height: 14px;
    aspect-ratio: 1/1;
    cursor: pointer;
    transition: width 0.1s, height 0.1s;
}

.SpeakMessageVolume:hover {
    width: 25px;
}