.navBar {
  background-color: #1f4379;
  height: 41px;
  padding: 0 12px;
}

.chatbot-v-align {
  display: flex;
  align-items: center;
}

.chatbot-history-icon {
  cursor: pointer;
  padding: 4px;
  padding-right: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  user-select: none;
}

.chatbot-history-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.chatbot-new-chat-icon {
  cursor: pointer;
  padding: 4px;
  padding-right: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  user-select: none;
}

.chatbot-new-chat-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.chatbot-new-chat-icon-placeholder {
  padding-right: 10px;
  width: 34px;
  height: 34px;
}

.title {
  font-size: 18px;
  color: white;
  font-weight: normal;
}

.chatbot-menus {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chatbot-menus span {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
  user-select: none;
}

.chatbot-menus span:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 遮罩層樣式 */
.historyOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 999;
  cursor: pointer;
}

/* 遮罩層淡入淡出動畫 */
.fade-enter-active {
  animation: fadeIn 0.2s ease-out;
}

.fade-leave-active {
  animation: fadeOut 0.2s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.version {
  text-align: right;
  font-size: 10px;
  color: gray;
}

body {
  overflow: hidden;
  background-color: #ffffff;
  outline: none;
  user-select: none;
  -webkit-user-select: none;
}

a {
  text-decoration: none;
}

a,
a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
}

.logout {
  color: #497cc7;
}

/* historyPanel 元件樣式（合併自 history.css，部分命名空間調整） */
#historyPanel {
  width: 220px;
  background-color: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);
  transition: left 0.2s;
}

.historyPanelContainer {
  display: flex;
}

.historyPanelPosition {
  position: absolute;
  top: 41px;
  left: 0;
  z-index: 1001;
}

/* 滑入動畫 */
.historyPanelPosition-enter-active {
  animation: slideInLeft 0.2s ease-out;
}

/* 滑出動畫 */
.historyPanelPosition-leave-active {
  animation: slideOutLeft 0.2s ease-in;
}


.mainFrame {
  width: 100%;
  height: calc(100vh);
  border: 0px;
}

.mainFrameDisplay {
  display: flex;
  flex: 1;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

#historyPanel .historyPanel {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  height: calc(100vh - 90px);
  overflow-y: auto;
  overflow-x: visible;
}

#historyPanel .historyList {
  list-style: none;
  margin: 0;
  padding: 0 10px 10px 10px;
  height: 100%;
}

#historyPanel .historyItem {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-size: 14px;
  padding: 10px 12px;
  transition: background-color 0.2s;
}

#historyPanel .historyItem:hover,
#historyPanel .historyItemClicked {
  background-color: #f5f5f5;
}

#historyPanel .historyItemContent {
  width: 100%;
  min-height: 15px;
  transition: background-color 0.2s;
}

#historyPanel .dotsBtn {
  display: none;
  width: 20px;
  height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  color: #3f3f3f;
  flex-shrink: 0;
}

#historyPanel .dotsBtn:hover {
  color: #000000;
}

#historyPanel .historyItem:hover .dotsBtn {
  display: flex;
}

#historyPanel .textWithMenu {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

#historyPanel .itemText {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 8px;
}

#historyPanel .itemMenu {
  position: fixed;
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  width: 127px;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 15px;
  z-index: 10000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 5px;
}

#historyPanel .menuItem-rename {
  display: flex;
  gap: 3px;
  border-bottom: 1px solid #D9D9D9;
  padding: 5px 12px;
  cursor: pointer;
  font-size: 14px;
  color: var(--Font-normal, #000);
  font-family: "SF Pro";
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
}

#historyPanel .menuItem-delete {
  display: flex;
  gap: 3px;
  padding: 5px 12px;
  cursor: pointer;
  font-size: 14px;
  color: var(--Font-normal, #000);
  font-family: "SF Pro";
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
}

#historyPanel .menuItem-rename:hover,
.menuItem-delete:hover {
  background-color: #e6e6e6;
  width: 115px;
  border-radius: 6px;
}

#historyPanel .historyItemEdit {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px;
}

#historyPanel .renameInput {
  flex: 1;
  padding: 3px 6px;
  font-size: 14px;
}

#historyPanel .categorizeText {
  font-size: 15px;
  color: #333;
  margin-left: 12px;
  font-weight: bold;
  padding-top: 20px;
  padding-bottom: 10px;
}

#historyPanel .loadingIndicator {
  display: flex;
  justify-content: center;
}

#historyPanel .ChatAddBtn {
  display: flex;
  align-items: center;
  justify-content: start;
  padding-left: 20px;
  cursor: pointer;
  font-size: 14px;
  width: 100%;
  height: 40px;
}

.dispearOutline {
  outline: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

@media screen and (max-width: 768px) {
  #historyPanel {
    left: 0;
    width: 220px;
    height: 100vh;
    z-index: 2000;
  }

  /* 手機版時遮罩層的 z-index 也需要調整 */
  .historyOverlay {
    top: 41px;
    z-index: 1999;
  }
}

/* Account View Styles */
#accountView {
  display: none;
  position: fixed;
  top: 45px;
  right: 12px;
  width: 181px;
  padding: 0px 1px;
  flex-direction: column;
  align-items: flex-start;
  border-top: 1px solid #D9D9D9;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #D9D9D9;
  border-radius: 15px;
  z-index: 1050;
}

#accountView.show {
  display: flex;
}

.account-user-info {
  display: flex;
  height: 60px;
  padding: 10px 9px;
  align-items: center;
  align-self: stretch;
  gap: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.account-user-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.account-user-details div {
  font-size: 14px;
  color: #333;
  line-height: 1.2;
}

.account-menu-item {
  display: flex;
  height: 40px;
  padding: 0px 9px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  position: relative;
}

.account-menu-item.single-content {
  flex-direction: column;
  justify-content: center;
}

.account-menu-item.account-version {
  font-size: 10px;
  color: gray;
  text-align: right;
  align-items: flex-end;
}

.account-menu-item.account-logout {
  border-top: 1px solid #f0f0f0;
}

.account-menu-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.account-auto-speech {
  display: flex;
  height: 40px;
  padding: 0px 9px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

/* Switch 滑動按鈕樣式 */
.switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 44px;
  height: 24px;
  flex-shrink: 0;
  margin-top: 5px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #34C759;
}

input:checked+.slider:before {
  transform: translateX(20px);
}

.logout {
  color: #497cc7;
  text-decoration: none;
  font-size: 14px;
}

.logout:hover {
  text-decoration: underline;
}