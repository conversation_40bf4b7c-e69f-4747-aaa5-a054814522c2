<!doctype html>
<html>

<head>
  <meta name="viewport" charset="utf-8"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

  <!-- 禁用快取 -->
  <meta http-equiv="Expires" content="0" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Cache-control" content="no-cache" />
  <meta http-equiv="Cache" content="no-cache" />

  <!-- 載入必要lib -->
  <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>
  <!-- 登入模組 -->
  <script src="../../ecp/EcpMemberLogin.js" charset="utf-8"></script>
  <script src="../../ecp/EcpUserLogin.js" charset="utf-8"></script>

  <script src="../../lib/vconsole/vconsole.min.js"></script>

  <script>
    document.oncontextmenu = new Function("return false");
    oncontextmenu = "return false;";
  </script>
</head>

<body onload="Main.doLoad()">
  <!-- 主要程式邏輯 -->
  <script src="main.js" charset="utf-8"></script>
  <script src="./viewController.js" charset="utf-8"></script>
  <link rel="stylesheet" type="text/css" href="./main.css" charset="utf-8" />

  <div id="App">
    <nav class="navbar navbar-light justify-content-between navBar">
      <div class="chatbot-v-align dispearOutline">
        <span class="chatbot-history-icon" v-bind:title="T['historyTitle']"
          v-if="UserInfo['userMode']!='anonymous' && isLogin" v-on:click="doHistoryToggle()">
          <img src="../../image/historyToggle.svg" />
        </span>
        <span id="newChatIcon" class="chatbot-new-chat-icon" v-bind:title="T['createNewChat']" v-on:click="doNewChat()">
          <img src="../../image/new_chat.svg" style="width: 22px; height: 22px" />
        </span>
        <span class="title">{{T['title']}}</span>
      </div>
      <div class="chatbot-menus">
        <span v-bind:title="T['pinCopilot']" v-on:click="doTogglePin()">
          <img v-if="pin" src="../../image/pin.svg" />
          <img v-else src="../../image/pin_disable.svg" />
        </span>
        <span id="mainSize" v-bind:title="T['displaySize']" v-on:click="cycleSizeChange()">
          <img src="../../image/page_size.svg" />
        </span>
        <span id="mainFontSize" v-bind:title="T['fontSize']" v-on:click="cycleFontSizeChange()">
          <img src="../../image/font_size.svg" />
        </span>
        <span id="userIcon" v-bind:title="T['userIconHint']" v-on:click="toggleMoreMenu()">
          <img src="../../image/more.svg" />
        </span>
        <span v-bind:title="T['closeChat']" style="cursor: pointer" v-on:click="doToggle">
          <img src="../../image/cancel.svg" />
        </span>
      </div>
    </nav>
    <div :class="{ 'historyPanelContainer': size === 'm' || size === 'l' }">
      <!-- 遮罩層 - 只在 size 為 's' 且歷史紀錄打開時顯示 -->
      <transition name="fade">
        <div v-if="size === 's' && showHistoryPanel" class="historyOverlay" @click="doHistoryToggle()"></div>
      </transition>
      <transition name="historyPanelPosition">
        <div id="historyPanel" :class="{ 'historyPanelPosition': size === 's'}" v-show="showHistoryPanel" @click.stop>
          <span class="ChatAddBtn dispearOutline" v-on:click="cleanMessageAndShowGreeting()">
            <img src="../../image/icon.png" alt="" width="20px">&nbsp;&nbsp;{{T['createNewChat']}}
          </span>
          <div class="historyPanel">
            <ul class="historyList">
              <!-- 今天 -->
              <div v-if="categorizedHistory['今天'].length">
                <li class="categorizeText" v-if="categorizedHistory['今天'].length > 0">今天</li>
                <li v-for="item in categorizedHistory['今天']" :key="item.FId" class="historyItem"
                  @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                  <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                    <div class="textWithMenu">
                      <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                      <span class="dotsBtn" @click.stop="toggleMenu(item.FId, $event)">
                        <img src="../../image/more-options.svg" />
                      </span>
                    </div>
                    <div class="itemMenu" v-if="showMenuId === item.FId">
                      <div class="menuItem-rename" @click="startRename(item.FId)"><img
                          src="../../image/edit.svg" />{{T['roomReNameText']}}</div>
                      <div class="menuItem-delete" v-if="!item.isCleanChat" @click.stop="deleteItem(item.FId)"><img
                          src="../../image/delete.svg" />{{T['roomDeleteText']}}</div>
                    </div>
                  </div>
                  <div v-else class="historyItemEdit">
                    <input v-focus v-model="tempName" @blur="applyRename(item.FId)"
                      @keyup.enter="applyRename(item.FId)" />
                  </div>
                </li>
              </div>

              <!-- 過去 7 天 -->
              <div v-if="categorizedHistory['過去 7 天'].length">
                <li class="categorizeText">過去 7 天</li>
                <li v-for="item in categorizedHistory['過去 7 天']" :key="item.FId" class="historyItem"
                  @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                  <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                    <div class="textWithMenu">
                      <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                      <span class="dotsBtn" @click.stop="toggleMenu(item.FId, $event)">
                        <img src="../../image/more-options.svg" />
                      </span>
                    </div>
                    <div class="itemMenu" v-if="showMenuId === item.FId">
                      <div class="menuItem-rename" @click="startRename(item.FId)"><img
                          src="../../image/edit.svg" />{{T['roomReNameText']}}</div>
                      <div class="menuItem-delete" @click.stop="deleteItem(item.FId)"><img
                          src="../../image/delete.svg" />{{T['roomDeleteText']}}</div>
                    </div>
                  </div>
                  <div v-else class="historyItemEdit">
                    <input v-focus v-model="tempName" @blur="applyRename(item.FId)"
                      @keyup.enter="applyRename(item.FId)" />
                  </div>
                </li>
              </div>

              <!-- 過去 30 天 -->
              <div v-if="categorizedHistory['過去 30 天'].length">
                <li class="categorizeText">過去 30 天</li>
                <li v-for="item in categorizedHistory['過去 30 天']" :key="item.FId" class="historyItem"
                  @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                  <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                    <div class="textWithMenu">
                      <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                      <span class="dotsBtn" @click.stop="toggleMenu(item.FId, $event)">
                        <img src="../../image/more-options.svg" />
                      </span>
                    </div>
                    <div class="itemMenu" v-if="showMenuId === item.FId">
                      <div class="menuItem-rename" @click="startRename(item.FId)"><img
                          src="../../image/edit.svg" />{{T['roomReNameText']}}</div>
                      <div class="menuItem-delete" @click.stop="deleteItem(item.FId)"><img
                          src="../../image/delete.svg" />{{T['roomDeleteText']}}</div>
                    </div>
                  </div>
                  <div v-else class="historyItemEdit">
                    <input v-focus v-model="tempName" @blur="applyRename(item.FId)"
                      @keyup.enter="applyRename(item.FId)" />
                  </div>
                </li>
              </div>

              <!-- 月份 -->
              <div v-for="(items, label) in categorizedHistory['月份']" :key="label">
                <li class="categorizeText">{{ label }}</li>
                <li v-for="item in items" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave"
                  :class="{ historyItemClicked: historyNowId === item.FId }">
                  <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                    <div class="textWithMenu">
                      <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                      <span class="dotsBtn" @click.stop="toggleMenu(item.FId, $event)">
                        <img src="../../image/more-options.svg" />
                      </span>
                    </div>
                    <div class="itemMenu" v-if="showMenuId === item.FId">
                      <div class="menuItem-rename" @click="startRename(item.FId)"><img
                          src="../../image/edit.svg" />{{T['roomReNameText']}}</div>
                      <div class="menuItem-delete" @click.stop="deleteItem(item.FId)"><img
                          src="../../image/delete.svg" />{{T['roomDeleteText']}}</div>
                    </div>
                  </div>
                  <div v-else class="historyItemEdit">
                    <input v-focus v-model="tempName" @blur="applyRename(item.FId)"
                      @keyup.enter="applyRename(item.FId)" />
                  </div>
                </li>
              </div>

              <!-- 年份 -->
              <div v-for="(items, label) in categorizedHistory['年份']" :key="label">
                <li class="categorizeText">{{ label }}</li>
                <li v-for="item in items" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave"
                  :class="{ historyItemClicked: historyNowId === item.FId }">
                  <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                    <div class="textWithMenu">
                      <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                      <span class="dotsBtn" @click.stop="toggleMenu(item.FId, $event)">
                        <img src="../../image/more-options.svg" />
                      </span>
                    </div>
                    <div class="itemMenu" v-if="showMenuId === item.FId">
                      <div class="menuItem-rename" @click="startRename(item.FId)"><img
                          src="../../image/edit.svg" />{{T['roomReNameText']}}</div>
                      <div class="menuItem-delete"click.stop="deleteItem(item.FId)"><img
                          src="../../image/delete.svg" />{{T['roomDeleteText']}}</div>
                    </div>
                  </div>
                  <div v-else class="historyItemEdit">
                    <input v-focus v-model="tempName" @blur="applyRename(item.FId)"
                      @keyup.enter="applyRename(item.FId)" />
                  </div>
                </li>
              </div>
              <div v-if="isHistoryListLoading" class="loadingIndicator">
                <img src="../../image/Loading.gif" style="width: 50px" alt="載入中..." />
              </div>
            </ul>
          </div>
        </div>
      </transition>
      <iframe class="mainFrame" :class="{ 'mainFrameDisplay': size === 'm' || size === 'l'}" id="mainFrame"></iframe>
    </div>
    <!-- Modal -->
    <!-- Account View -->
    <div id="accountView">
      <div class="account-user-info">
        <img v-if="!isCustomCssEnable" onerror="this.src='../../image/person.svg'" v-bind:src="UserInfo['userIcon']"
          width="40px" style="border-radius: 30px" />
        <img v-if="isCustomCssEnable" onerror="this.src='../../image/user_custom.png'" v-bind:src="UserInfo['userIcon']"
          width="40px" style="border-radius: 30px" />
        <div class="account-user-details">
          <div>{{userTypeContent}}</div>
          <div>{{UserInfo['userName']}}</div>
        </div>
      </div>
      <div v-if="showAutoSpeech" class="account-auto-speech">
        <div class="account-menu-label">{{T['webSpeechSynthesis']}}</div>
        <label class="switch">
          <input type="checkbox" v-model="AutoSpeech" v-on:change="switchAutoSpeech()">
          <span class="slider"></span>
        </label>
      </div>

      <div class="account-menu-item account-version single-content">
        <div>{{version}}</div>
      </div>
      <div class="account-menu-item account-logout single-content">
        <a class="logout" href="#" v-if="showLogout && !isTeamsChannel" v-on:click="doLogout()">{{T['logout']}}</a>
        <a class="logout" href="#" v-if="showLeave && !isTeamsChannel" v-on:click="doLogout()">{{T['leave']}}</a>
      </div>
    </div>
    <div id="loading" class="progressDiv animate__animated animate__fadeIn">
      <div>
        <font style="color: gray">{{T['please_wait']}}</font>
        <div class="progress">
          <div class="progressBar progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
            v-bind:aria-valuenow="progress" aria-valuemin="0" aria-valuemax="100" v-bind:style="{width:progress+'%'}">
            {{progress}} %</div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>