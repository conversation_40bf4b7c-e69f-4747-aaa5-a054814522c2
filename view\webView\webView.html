<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" charset="utf-8" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <!-- 禁用快取 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <!-- 載入必要lib -->
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>

    <link rel=stylesheet type="text/css" href="./webView.css" charset="utf-8">


    <script>
        document.oncontextmenu = new Function("return false");
        oncontextmenu = "return false;"
    </script>
</head>

<body onload="WebView.doLoad()">

    <!-- 主要程式邏輯 -->
    <script src="webView.js" charset="utf-8"></script>
    <script src="../chat/chatEvent.js" charset="utf-8"></script>

    <div id="App">
        <nav class="navbar navbar-light justify-content-center navBar">
            <a class="animate__animated animate__fadeIn webviewback" v-if="IsBackShow" v-on:click="doBackList"><img class="webviewbackIcon" src="../../image/back.png"></a>
            <font class="title" v-bind:title="WebTitle">{{WebTitle.substr(0,45)+(WebTitle.length>45?'...':'')}}</font>
            <a class="webviewClose" v-on:click="doCloseView">
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_d_438_3993)">
                        <circle cx="12" cy="12.5" r="10" fill="white"></circle>
                    </g>
                    <path d="M17.3791 6.58217C17.5731 6.45414 17.8372 6.4758 18.008 6.64662C18.1787 6.81743 18.2004 7.08147 18.0724 7.27553L18.008 7.35365L12.7834 12.5773L18.0089 17.8019C18.2042 17.9971 18.204 18.3137 18.0089 18.5089C17.8137 18.7042 17.4972 18.7042 17.3019 18.5089L12.0773 13.2843L6.85367 18.5089C6.65841 18.7042 6.34189 18.7042 6.14663 18.5089C5.95151 18.3136 5.95142 17.9971 6.14663 17.8019L11.3693 12.5773L6.14663 7.35463L6.08218 7.2765C5.95412 7.08245 5.97581 6.81841 6.14663 6.6476C6.31747 6.47688 6.58151 6.45511 6.77554 6.58314L6.85367 6.6476L12.0763 11.8703L17.3009 6.64662L17.3791 6.58217Z" fill="black"></path>
                    <defs>
                        <filter id="filter0_d_438_3993" x="0" y="0.5" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset></feOffset>
                            <feGaussianBlur stdDeviation="1"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"></feColorMatrix>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_438_3993"></feBlend>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_438_3993" result="shape"></feBlend>
                        </filter>
                    </defs>
                </svg></a>
        </nav>
        <div v-if="'LIST' == Mode" class="animate__animated animate__fadeIn mainView">
            <p>
                <font class="question">{{T['question']}}{{InputQuestion}}</font>
            </p>
            <p>{{T['viewMoreTitle']}}</p>
            <div class="card m-2" v-for="(item, index) in ListData">
                <div class="card-header">
                    <div>
                        <font style="cursor: pointer;color: #007bff;" v-on:click="doOpenDetail('km',item.metadata.sourceName,item.metadata.knowledgeId)">
                            {{item.metadata.sourceName}}</font><img style="cursor: pointer;margin-left:5px" v-if="item.metadata.type=='file'" src="../../image/file.png" width="15px" v-on:click="doOpenDetail('file',item.metadata.sourceName,item.metadata.sourceId)">
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text" v-html="item.page_content"></p>
                </div>
            </div>
        </div>
        <div v-if="'HTML' == Mode" class="animate__animated animate__fadeIn mainView">
            <div v-if="attachments.length>0">
                <p>{{T['downloadList']}}</p>
                <table class="table table-sm text-center" style="table-layout: fixed;">
                    <thead>
                        <tr style="background-color:rgba(139, 168, 217, 0.3); font-size:14px">
                            <th scope="col" style="width:30px;"></th>
                            <th scope="col" style="width:95px;">操作</th>
                            <th scope="col">{{T['fileName']}}</th>
                            <th scope="col" style="width:160px;">{{T['fileUploadTime']}}</th>
                            <th scope="col" style="width:100px;">{{T['fileSize']}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in attachments">
                            <th scope="row">{{index+1}}</th>
                            <td>
                                <div class="d-flex justify-content-between" style="padding-left: 6px;padding-right: 6px;">
                                    <a href="#" v-if="item.FName.toLowerCase().includes('.pdf')" @click="downloadFile(item.FId, getFileNameWithoutExt(item.FName), true)">
                                        閱覽
                                    </a>
                                    <span v-else class="text-secondary">閱覽</span>
                                    <a href="#" @click="downloadFile(item.FId, getFileNameWithoutExt(item.FName), false)">下載</a>
                                </div>
                            </td>
                            <td v-bind:title="item.FName" class="text-truncate text-left">
                                {{item.FName}}
                            </td>
                            <td>{{new Date(item.FUploadTime).Format("yyyy-MM-dd HH:mm:ss")}}</td>
                            <td>{{Number.parseFloat(item.FSize / 1024).toFixed(2)}} KB</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div id="mainFrame" v-html="HtmlView"></div>
        </div>
        <iframe v-if="'URL' == Mode" class="animate__animated animate__fadeIn mainWebView" id="mainWebFrame" v-bind:src="WebViewSrc"></iframe>
    </div>
    </div>
</body>

</html>