var QbiAudio = {
    TEMPLATE: `
    <div class="audio-container">
        <div class="audio-player">
            <img class="play-pause-btn" src="../../image/play.svg" onclick="QbiAudio.togglePlay(this)" />
            <div class="sound-wave" onclick="QbiAudio.seekToPosition(event)" style="cursor: pointer;">
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
                <div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div><div class="sound-bar"></div>
            </div>
            <span class="audio-time">0:00</span>
        </div>
        <audio controls title="音訊播放器" style="display: none;">
            <source src='{sourceUrl}' type='audio/ogg'>
            <source src='{sourceUrl}' type='audio/mpeg'>
        </audio>
    </div>
    `,

    // 全域變數來追蹤拖曳狀態
    dragState: {
        isDragging: false,
        currentSoundWave: null
    },

    // 初始化全域拖曳事件監聽器（只執行一次）
    initGlobalDragSeek() {
        // 避免重複初始化
        if (QbiAudio.globalDragInitialized) return;
        QbiAudio.globalDragInitialized = true;

        // 使用事件委派，監聽所有 sound-wave 的拖曳事件
        document.addEventListener('mousedown', (e) => {
            const soundWave = e.target.closest('.sound-wave');
            if (!soundWave) return;

            QbiAudio.dragState.isDragging = true;
            QbiAudio.dragState.currentSoundWave = soundWave;
            QbiAudio.seekToPosition(e);
        });

        document.addEventListener('mousemove', (e) => {
            if (!QbiAudio.dragState.isDragging || !QbiAudio.dragState.currentSoundWave) return;

            const soundWave = QbiAudio.dragState.currentSoundWave;
            const rect = soundWave.getBoundingClientRect();
            const bars = soundWave.querySelectorAll('.sound-bar');
            const totalBars = bars.length;
            const audio = soundWave.closest('.audio-container').querySelector('audio');
            
            if (audio.duration > 0) {
                // 獲取第一個和最後一個波形條的實際位置
                const firstBar = bars[0];
                const lastBar = bars[totalBars - 1];
                const firstBarRect = firstBar.getBoundingClientRect();
                const lastBarRect = lastBar.getBoundingClientRect();
                
                // 計算波形條群實際佔據的寬度
                const barGroupStart = firstBarRect.left - rect.left;
                const barGroupEnd = lastBarRect.right - rect.left;
                const totalBarWidth = barGroupEnd - barGroupStart;
                
                // 計算拖曳位置相對於波形條群的位置
                const clickX = e.clientX - rect.left;
                const relativeClickX = clickX - barGroupStart;
                
                // 確保拖曳在波形條群範圍內
                if (relativeClickX >= 0 && relativeClickX <= totalBarWidth) {
                    const progress = (relativeClickX / totalBarWidth) * 100;
                    const seekTime = Math.max(0, Math.min(audio.duration, (progress / 100) * audio.duration));
                    audio.currentTime = seekTime;
                    
                    // 拖曳時更新波形條顏色
                    QbiAudio.updateWaveformColors(soundWave, progress);
                }
            }
        });

        document.addEventListener('mouseup', () => {
            QbiAudio.dragState.isDragging = false;
            QbiAudio.dragState.currentSoundWave = null;
        });

        document.addEventListener('mouseleave', () => {
            QbiAudio.dragState.isDragging = false;
            QbiAudio.dragState.currentSoundWave = null;
        });
    },

    create(answer) {
        let html = QbiAudio.TEMPLATE;
        html = html.replaceAll('{sourceUrl}', answer.originalContentUrl);
        
        // 確保全域拖曳功能已初始化
        QbiAudio.initGlobalDragSeek();
        
        // 延遲初始化波形分析
        setTimeout(() => {
            const audioContainers = document.querySelectorAll('.audio-container');
            const lastContainer = audioContainers[audioContainers.length - 1];
            const soundWave = lastContainer ? lastContainer.querySelector('.sound-wave') : null;
            
            if (soundWave) {
                QbiAudio.analyzeAndSetWaveform(soundWave);
            }
        }, 100);
        
        return html;
    },

    togglePlay(button) {
        const container = button.closest('.audio-container');
        const audio = container.querySelector('audio');
        const soundWave = container.querySelector('.sound-wave');
        const timeDisplay = container.querySelector('.audio-time');
        
        if (audio.paused) {
            audio.play();
            button.src = "../../image/stop.svg";
            soundWave.classList.add('playing');
            
            // 更新播放时间和进度显示
            audio.ontimeupdate = function() {
                const minutes = Math.floor(audio.currentTime / 60);
                const seconds = Math.floor(audio.currentTime % 60);
                timeDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                // 更新波形條顏色
                if (audio.duration > 0) {
                    const progress = (audio.currentTime / audio.duration) * 100;
                    QbiAudio.updateWaveformColors(soundWave, progress);
                }
            };
        } else {
            audio.pause();
            button.src = "../../image/play.svg";
            soundWave.classList.remove('playing');
        }
        
        // 音频结束时重置
        audio.onended = function() {
            button.src = "../../image/play.svg";
            soundWave.classList.remove('playing');
            timeDisplay.textContent = "0:00";
            
            // 重置波形條顏色為白色
            QbiAudio.updateWaveformColors(soundWave, 0);
        };
    },

    // 點擊和拖曳波形條調整進度
    seekToPosition(event) {
        const soundWave = event.currentTarget;
        const container = soundWave.closest('.audio-container');
        const audio = container.querySelector('audio');
        
        if (audio.duration > 0) {
            const rect = soundWave.getBoundingClientRect();
            const bars = soundWave.querySelectorAll('.sound-bar');
            const totalBars = bars.length;
            
            // 獲取第一個和最後一個波形條的實際位置
            const firstBar = bars[0];
            const lastBar = bars[totalBars - 1];
            const firstBarRect = firstBar.getBoundingClientRect();
            const lastBarRect = lastBar.getBoundingClientRect();
            
            // 計算波形條群實際佔據的寬度
            const barGroupStart = firstBarRect.left - rect.left;
            const barGroupEnd = lastBarRect.right - rect.left;
            const totalBarWidth = barGroupEnd - barGroupStart;
            
            // 計算點擊位置相對於波形條群的位置
            const clickX = event.clientX - rect.left;
            const relativeClickX = clickX - barGroupStart;
            
            // 確保點擊在波形條群範圍內
            if (relativeClickX >= 0 && relativeClickX <= totalBarWidth) {
                const progress = (relativeClickX / totalBarWidth) * 100;
                const seekTime = (progress / 100) * audio.duration;
                
                audio.currentTime = seekTime;
                
                // 立即更新波形條顏色
                QbiAudio.updateWaveformColors(soundWave, progress);
            }
        }
    },

    // 保留原有的方法以向後相容，但現在使用全域事件委派
    initDragSeek(soundWave) {
        // 這個方法現在只是為了向後相容，實際功能已經移到全域事件委派
        console.log('QbiAudio: 使用全域事件委派處理拖曳功能，無需單獨初始化');
    },

    // 分析音頻並設置波形
    async analyzeAndSetWaveform(soundWave) {
        const audio = soundWave.closest('.audio-container').querySelector('audio');
        const bars = soundWave.querySelectorAll('.sound-bar');
        
        try {
            // 等待音頻加載
            if (audio.readyState < 2) {
                await new Promise((resolve) => {
                    audio.addEventListener('loadeddata', resolve, { once: true });
                });
            }
            
            // 獲取音頻數據
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const response = await fetch(audio.src);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            
            // 分析音頻數據
            const channelData = audioBuffer.getChannelData(0);
            const samplesPerBar = Math.floor(channelData.length / bars.length);
            const waveformData = [];
            
            for (let i = 0; i < bars.length; i++) {
                const start = i * samplesPerBar;
                const end = Math.min(start + samplesPerBar, channelData.length);
                const segment = channelData.slice(start, end);
                
                // 計算RMS（均方根）音量
                let sum = 0;
                for (let j = 0; j < segment.length; j++) {
                    sum += segment[j] * segment[j];
                }
                const rms = Math.sqrt(sum / segment.length);
                
                // 檢查是否有聲音（閾值）
                const hasSound = rms > 0.01; // 可調整的閾值
                waveformData.push({
                    hasSound: hasSound,
                    volume: hasSound ? rms : 0
                });
            }
            
            // 設置波形條高度
            QbiAudio.setWaveformBars(bars, waveformData);
            
        } catch (error) {
            console.log('音頻分析失敗，使用默認波形:', error);
            // 如果分析失敗，使用默認的靜態波形
            QbiAudio.setDefaultWaveform(bars);
        }
    },

    // 設置波形條高度
    setWaveformBars(bars, waveformData) {
        bars.forEach((bar, index) => {
            const data = waveformData[index];
            
            if (data.hasSound) {
                // 有聲音的條：根據音量設置高度
                const minHeight = 3;
                const maxHeight = 20;
                const height = minHeight + (data.volume * (maxHeight - minHeight) * 10);
                bar.style.height = `${Math.max(minHeight, Math.min(maxHeight, height))}px`;
                bar.style.opacity = '1';
            } else {
                // 沒有聲音的條：設置為最小高度並降低透明度
                bar.style.height = '2px';
                bar.style.opacity = '0.3';
            }
        });
    },

    // 設置默認波形（靜態）
    setDefaultWaveform(bars) {
        bars.forEach((bar, index) => {
            // 使用原來的靜態高度規則
            if (index % 3 === 0) bar.style.height = '8px';
            else if (index % 5 === 0) bar.style.height = '12px';
            else if (index % 7 === 0) bar.style.height = '6px';
            else if (index % 11 === 0) bar.style.height = '16px';
            else if (index % 13 === 0) bar.style.height = '10px';
            else if (index % 17 === 0) bar.style.height = '20px';
            else bar.style.height = '3px';
            
            bar.style.opacity = '1';
        });
    },

    // 更新波形條顏色 - 已播放部分為黑色，未播放部分為白色
    updateWaveformColors(soundWave, progress) {
        const bars = soundWave.querySelectorAll('.sound-bar');
        const totalBars = bars.length;
        const playedBars = Math.floor((progress / 100) * totalBars);
        
        bars.forEach((bar, index) => {
            if (index < playedBars) {
                // 已播放部分 - 黑色
                bar.style.backgroundColor = '#000000';
            } else {
                // 未播放部分 - 白色
                bar.style.backgroundColor = '#9a9a9a';
            }
        });
    }
};








