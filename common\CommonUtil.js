var CommonUtil = {

    // 寫入資料 
    saveData(key, id, data) {
        let content = CommonUtil.getLocalStorage(key);
        if (content == null) {
            content = {};
        }
        content[id] = data;
        CommonUtil.setLocalStorage(key, JSON.stringify(content));
    },

    // 提取資料
    getData(key, id) {
        let content = CommonUtil.getLocalStorage(key);
        return content[id];
    },

    //  是否為Json
    isJson: function (str) {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    },

    // string check
    isStringEmpty(str) {
        if (str !== undefined && str !== null && str.length > 0) {
            return false;
        }
        return true;
    },

    // 是否為手機裝置
    isMobile: function () {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 設定Cookie
    setCookie: function (name, value, days) {
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000); // ) removed
            var expires = "; expires=" + date.toGMTString(); // + added
        } else var expires = "";
        document.cookie = name + "=" + value + expires + ";path=/"; // + and " added
    },

    // 清除Cookie
    clearCookie: function (name) {
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
    },

    // 設定LocalStorage
    setLocalStorage: function (key, data) {
        localStorage.setItem(key, data);
    },

    // 刪除LocalStorage
    deleteLocalStorage: function (key) {
        localStorage.removeItem(key);
    },

    // 取得LocalStorage
    getLocalStorage: function (key) {
        let data = localStorage.getItem(key);
        if (this.isJson(data)) {
            data = JSON.parse(data);
        }
        return data;
    },

    //取得cookie
    getCookie: function (cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(";");
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == " ") {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    },

    // 取得網址參數
    getURLParameter: function (queryString, paramater) {
        let urlParams = new URLSearchParams(queryString);
        return urlParams.get(paramater);
    },

    // 過濾HTML格式
    stripHTML: function (input, filterLine) {
        var output = '';
        if (typeof (input) == 'string') {
            var output = input.replace(/(<([^>]+)>)/ig, "");
            if (filterLine) {
                output = output.replace(/\r\n|\n|\/|\\|\"|\'|&nbsp;|&quot;/g, "");
            } else {
                output = output.replace(/\/|\\|\"|&nbsp;|&quot;/g, "");
            }
        }
        return output;
    },

    /** 時間轉HH:mm:ss 字串格式 */
    formatStringDate: function (value) {
        if (!value) return '';
        return new Date(value).toLocaleTimeString('en-US', { hour12: false });
    },

    /** 時間轉HH:mm 字串格式 */
    formatStringDateShort: function (value) {
        if (!value) return '';
        return new Date(value).toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    // 取得UUID
    getRandomUuid: function () {
        let index = 0; // 陣列的索引
        let array = new Uint8Array(31);
        crypto.getRandomValues(array);
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = array[index] % 16;
            index++;
            return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        });
    },

    swalWarnMessage(configTextName) {
        Swal.fire({
            text: Text[configTextName],
            icon: "warning",
        });
    },

    // 當前是否為Teams模式
    isTeamsChannel() {
        return 'teams' == CommonUtil.getLocalStorage('source');
    },
    send(targetId, type, data) {
        document.getElementById("targetId")
        const iframe =
          parent.document.getElementById(targetId) ||
          (parent.parent && parent.parent.document.getElementById(targetId));
    
        const targetURL = new URL(iframe.src);
        const targetHost = targetURL.protocol + "//" + targetURL.host;
        if (iframe && iframe.contentWindow) {
          iframe.contentWindow.postMessage({ type, data }, targetHost);
        } else {
          console.warn(`[CommonFrameMessageCenter] 找不到 iframe: ${targetId}`);
        }
      },
    
      // 註冊多個訊息處理器：{ type: handlerFn }
      listen(messageMap) {
        if (!messageMap || typeof messageMap !== "object") return;
    
        window.addEventListener("message", (event) => {
          const { type, data } = event.data || {};
          const handler = messageMap[type];
          if (typeof handler === "function") {
            handler(data);
          }
        });
      }
}