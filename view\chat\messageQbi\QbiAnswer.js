var QbiAnswer = {
  TEMPLATE: `
    <div class="QbiAnswer_Container">
            {contentArray}
            <div class="MessageText_Satis">
                <div class="satisfactionBar animate__animated animate__fadeInUp">
                    {downloadButton}
                    <image src="../../image/copy.svg" class="satisfactionBtnCopy" title="{satisfactCopyText}"
                    onclick="ChatEvent.onCopyClick(this,'{copyString}');"></image>
                    <image id="{messageId}0" src="../../image/ok.svg" class="satisfactionBtnOk" title="{satisfactOKText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','0');"></image>
                    <image id="{messageId}-1" src="../../image/dislike.svg" class="satisfactionBtnDislike" title="{satisfactDislikeText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','-1');"></image>
                    <img class="SpeakMessageVolume" title="{SpeakMessageVolumeHint}" src="../../image/SingalSpeech.svg" onclick="WebSpeechSynthesis.speak('{speechSynthesisString}')">
                </div>
            </div>
    </div>
    `,
  MESSAGE_TEMPLATE: `
        <div class="MessageText_WithTimestamp">
            <div class="MessageText" >
                <font class="Copy-{copyId} WordBreakAll">{content}</font>
            </div>
            <div class="MessageText_Timestamp" style="font-size: 14px;">{timestamp}</div>
        </div>
        `,
  MESSAGE_TEMPLATE_GENERALTEXT: `
    <div style="padding-top: 10px;">
        <div style="display: inline-block;width:100%"><font style="color:rgb(234, 107, 102); font-size: smaller;">{gptAlert}</font></div>
    </div>
  `,
  MESSAGE_TEMPLATE_VIEWNORE: `
        <div class="MessageText_WithTimestamp">
            <div class="MessageText">
                <font class="Copy-{copyId} WordBreakAll">{content}</font>
                <div class="ViewMoreContainer">
                    <span class="ViewMoreText">
                        <a href="#{messageId}"
                            onclick="ChatEvent.onViewMoreClick(this,'{inputQuestion}','{messageId}');">{viewMore}</a>
                    </span>
                </div>
            </div>
            <div class="MessageText_Timestamp">{timestamp}</div>
        </div>
    `,
  NO_MESSAGEID_TEMPLATE: `
        {contentArray}
        <div class="MessageText_Satis">
        </div>
    `,

  doInit() {
    ThirdPartyTool.includeCSS("./messageQbi/view/css/QbiAnswerStyle.css");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiText.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiImage.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiCard.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiAudio.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiVideo.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiFile.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiQuickReply.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiWebView.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiMediaCard.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiJson.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiExternal.js");
    setTimeout(function () {
      QbiQuickReply.initQuickReplyPool();
      QbiAnswer["Text"] = QbiText.create;
      QbiAnswer["Html"] = QbiText.create;
      QbiAnswer["Image"] = QbiImage.create;
      QbiAnswer["LinkImage"] = QbiImage.create;
      QbiAnswer["Cards"] = QbiCard.create;
      QbiAnswer["List"] = QbiCard.create;
      QbiAnswer["Audio"] = QbiAudio.create;
      QbiAnswer["Video"] = QbiVideo.create;
      QbiAnswer["File"] = QbiFile.create;
      QbiAnswer["QuickReply"] = QbiQuickReply.create;
      QbiAnswer["WebView"] = QbiWebView.create;
      QbiAnswer["MediaCard"] = QbiMediaCard.create;
      QbiAnswer["Json"] = QbiJson.create;
      QbiAnswer["External"] = QbiExternal.create;
    }, 1000);
  },

  doDisplayAnswer(messageId, content, generalText) {
    if (typeof content === "string") content = JSON.parse(content);
    let answer = content;
    let timestamp = Chat.Chat_HistoryTimestamp || CommonUtil.formatStringDateShort(new Date());
    let answerType = answer.type.charAt(0).toUpperCase() + answer.type.slice(1);
    let html =
      messageId == null ? QbiAnswer.NO_MESSAGEID_TEMPLATE : QbiAnswer.TEMPLATE;
    let contentArray = "";
    let hasDownload = false;
    let downloadType = "";

    if (answerType == "Multiple") {
      answer.ans.forEach((contentItem, index) => {
        const isLast = index === answer.ans.length - 1;
        contentArray += QbiAnswer.addMessage(contentItem.type, contentItem, isLast, messageId, generalText);
        // 檢查是否有圖片、影片、檔案或媒體卡片類型
        if ((contentItem.type === "Image" || contentItem.type === "Video" || contentItem.type === "File") && contentItem.downloadHref) {
          hasDownload = true;
          downloadType = contentItem.downloadType || contentItem.type;
          answer.downloadHref = contentItem.downloadHref;
          answer.downloadName = contentItem.downloadName;
        } else if (contentItem.type === "MediaCard" && contentItem.downloadItems && contentItem.downloadItems.length > 0) {
          // MediaCard 有多個下載項目，需要特殊處理
          hasDownload = true;

          // 如果還沒有 downloadItems 數組，初始化它
          if (!answer.downloadItems) {
            answer.downloadItems = [];
          }

          // 將所有下載項目添加到答案的 downloadItems 數組中
          let downloadItems = [...contentItem.downloadItems];
          downloadItems[0].name = contentItem?.name || contentItem.downloadItems[0].name;
          answer.downloadItems = answer.downloadItems.concat(downloadItems);

          // 設置第一個項目作為主要下載信息（向後兼容）
          if (!answer.downloadHref) {
            downloadType = contentItem.downloadItems[0].type;
            answer.downloadHref = contentItem.downloadItems[0].href;
            answer.downloadName = contentItem?.name || contentItem.downloadItems[0].name;
          }
        } else if (contentItem.type === "QuickReply" && contentItem.QuickReply &&
                   contentItem.QuickReply.type === "MediaCard" &&
                   contentItem.QuickReply.downloadItems &&
                   contentItem.QuickReply.downloadItems.length > 0) {
          // QuickReply 內部的 MediaCard 處理
          hasDownload = true;

          // 如果還沒有 downloadItems 數組，初始化它
          if (!answer.downloadItems) {
            answer.downloadItems = [];
          }

          // 將所有下載項目添加到答案的 downloadItems 數組中
          let downloadItems = [...contentItem.QuickReply.downloadItems];
          downloadItems[0].name = contentItem.QuickReply?.name || contentItem.QuickReply.downloadItems[0].name;
          answer.downloadItems = answer.downloadItems.concat(downloadItems);
          // 設置第一個項目作為主要下載信息（向後兼容）
          if (!answer.downloadHref) {
            downloadType = contentItem.QuickReply.downloadItems[0].type;
            answer.downloadHref = contentItem.QuickReply.downloadItems[0].href;
            answer.downloadName = contentItem.QuickReply.downloadItems[0].name;
          }
        }
      });
    } else {
      contentArray = QbiAnswer.addMessage(answerType, answer, true, messageId, generalText);
      // 檢查單一答案是否為圖片、影片、檔案或媒體卡片類型
      if ((answerType === "Image" || answerType === "Video" || answerType === "File") && answer.downloadHref) {
        hasDownload = true;
        downloadType = answer.downloadType || answerType;
      } else if (answerType === "MediaCard" && answer.downloadItems && answer.downloadItems.length > 0) {
        // MediaCard 有多個下載項目，需要特殊處理
        hasDownload = true;
        // 設置第一個項目作為主要下載信息（向後兼容）
        downloadType = answer.downloadItems[0].type;
        answer.downloadHref = answer.downloadItems[0].href;
        answer.downloadName = answer.downloadItems[0].name;
      } else if (answerType === "MediaCard" && answer.downloadHref) {
        // 回退檢查：如果沒有 downloadItems 但有 downloadHref
        hasDownload = true;
        downloadType = answer.downloadType || "MediaCard";
      }
    }

    let downloadButton = "";

    if (hasDownload) {
      // 檢查是否有多個下載項目（MediaCard）
      if (answer.downloadItems && answer.downloadItems.length > 1) {
        // 生成多個下載按鈕
        for (let i = 0; i < answer.downloadItems.length; i++) {
          const item = answer.downloadItems[i];
          const downloadId = `download_${CommonUtil.getRandomUuid()}`;
          let downloadTitle = item.type === "Video" ? Text['Download'] + Text['videoLink'] : Text['Download'] + Text['pictureLink'];
          let buttonText = item.type === "Video" ? Text['Download'] : Text['Download'];
          let buttonHtml = `<a href="javascript:void(0)" class="satisfactionBtnDownload" onclick="QbiAnswer.downloadFile('${item.href}', '${item.name}', '${item.type}')" title="${downloadTitle}">${buttonText}</a> `;
          downloadButton += buttonHtml;
        }
      } else {
        // 單一下載按鈕（原有邏輯）

        // 如果有 downloadItems，使用第一個項目的類型
        let actualDownloadType = downloadType;
        if (answer.downloadItems && answer.downloadItems.length > 0) {
          actualDownloadType = answer.downloadItems[0].type;
        }

        const downloadId = `download_${CommonUtil.getRandomUuid()}`;
        let downloadTitle = actualDownloadType === "Video" ? Text['Download'] + Text['videoLink'] : Text['Download'] + Text['pictureLink'];
        downloadButton = `<a href="javascript:void(0)" class="satisfactionBtnDownload" onclick="QbiAnswer.downloadFile('${answer.downloadHref}', '${answer.name}', '${actualDownloadType}')" title="${downloadTitle}">` + Text['Download'] + `</a > `;
      }
    }

    html = html.replaceAll("{contentArray}", contentArray);
    html = html.replaceAll("{downloadButton}", downloadButton);
    html = html.replaceAll("{messageId}", messageId);
    let copyId = CommonUtil.getRandomUuid();
    html = html.replaceAll("{copyString}", copyId);
    html = html.replaceAll("{copyId}", copyId);
    html = html.replace("{satisfactLikeText}", Text["satisfactLikeText"]);
    html = html.replace("{satisfactOKText}", Text["satisfactOKText"]);
    html = html.replace("{satisfactDislikeText}", Text["satisfactDislikeText"]);
    html = html.replace("{satisfactCopyText}", Text["satisfactCopyText"]);
    html = html.replaceAll("{SpeakMessageVolumeHint}", Text["SpeakMessageVolumeHint"]);
    html = html.replaceAll("{timestamp}", timestamp);
    html = html.replaceAll("{speechSynthesisString}", CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis, true));
    html = html.replaceAll("{viewMore}", Text["viewMore"]);
    html = html.replaceAll("{inputQuestion}", Chat.Chat_inputQuestion.replace(/'/g, "\\'"));

    if (CodeParse.isContainCode(html)) {
      html = CodeParse.getContentParse(html);
    }
    QbiAnswer.handleAIMessageInfo(answerType, answer, messageId);
    MessageController.doAddMessage(html, MessageController.LEFT);
    Chat.PageApp.LOCK_SEND = false;

  },

  addMessage(answerType, content, isLast, messageId, generalText) {
    //判斷是否為多答數組最後一位以處理(QuickReply及WebView會出現在多答數組的最後位置)
    if (answerType === "QuickReply" && content.hasOwnProperty("QuickReply")) {
      //處理QuickReply
      if (content.QuickReply.hasOwnProperty("quick_reply_items")) {
        QbiAnswer["QuickReply"](content.QuickReply);
      }
      //處理WebView
      if (content.QuickReply.hasOwnProperty("WebViewUrl")) {
        QbiAnswer["WebView"](content.QuickReply.WebViewUrl);
      }
      //處理答案格式
      if (content.QuickReply.hasOwnProperty("type")) {
        return QbiAnswer.doFilterQbiMessage(
          QbiAnswer[content.QuickReply.type](content.QuickReply),
          isLast,
          messageId,
          content.QuickReply.type,
          generalText
        );
      }
    } else {
      //處理WebView
      if (content.hasOwnProperty("WebViewUrl")) {
        QbiAnswer["WebView"](content.WebViewUrl);
      }
      //處理答案格式
      if (content.hasOwnProperty("type")) {
        return QbiAnswer.doFilterQbiMessage(
          QbiAnswer[answerType](content),
          isLast,
          messageId,
          answerType,
          generalText
        );
      }
    }
  },

  doFilterQbiMessage(ShowContent, isLast, messageId, answerType, generalText) {
    let copyContent = "";
    let data = CommonUtil.getLocalStorage(MessageController.StorageTOPN_KEY)[messageId];
    let TEMPLATE =
      isLast && !(data == null)
        ? this.MESSAGE_TEMPLATE_VIEWNORE
        : this.MESSAGE_TEMPLATE;
    if (generalText && generalText !== "EXECUTE_EXPLORE_MAP" && answerType === "Text") {
      const closingDivIndex = TEMPLATE.indexOf('</div>');
      let genGptText = "";
      switch (generalText) {
        case "EXECUTE_PROMPT":
          genGptText = Chat.PageApp.ServerConfig?.aIPromptGenerateText == null ? Text['gptAlert'] : Chat.PageApp.ServerConfig.aIPromptGenerateText;
          break;
        default:
          genGptText = Chat.PageApp.ServerConfig?.functionGenerateText == null ? Text['gptAlert'] : Chat.PageApp.ServerConfig.functionGenerateText;
          break;
      }
      let templateTemp = this.MESSAGE_TEMPLATE_GENERALTEXT.replace('{gptAlert}', genGptText === "" ? generalText : genGptText);
      TEMPLATE = TEMPLATE.slice(0, closingDivIndex) + templateTemp + TEMPLATE.slice(closingDivIndex);
    }
    TEMPLATE = TEMPLATE.replaceAll("{content}", ShowContent);

    //修正套件跑版問題
    if (answerType === "Cards" || answerType === "MediaCard") {
      TEMPLATE = TEMPLATE.replaceAll("MessageText_WithTimestamp", "MessageText_WithTimestamp MessageCard");
    }
    QbiAnswer.saveTotalMessage_Synthesis(answerType, ShowContent);

    return TEMPLATE;
  },

  saveTotalMessage_Synthesis(answerType, ShowContent) {
    let Message_Synthesis = ""
    switch (answerType) {
      case "Image":
        Message_Synthesis += Text["image"]
        break;
      case "LinkImage":
        Message_Synthesis += Text["image"]
        break;
      case "Audio":
        Message_Synthesis += Text["audio"]
        ShowContent = ShowContent.replaceAll("0:00", "");
        break;
      case "Video":
        Message_Synthesis += Text["video"]
        break;
      case "File":
        Message_Synthesis += Text["file"]
        break;
      case "MediaCard":
        if (ShowContent.includes("影片連結")) Message_Synthesis += Text["video"];
        if (ShowContent.includes("圖片連結")) Message_Synthesis += Text["image"];
        break;
    }
    Message_Synthesis += CommonUtil.stripHTML(ShowContent, true);
    MessageController.TotalMessage_Synthesis += Message_Synthesis;
  },
  handleAIMessageInfo(answerType, answer, messageId) {
    //儲存對話紀錄
    if (answerType === "List") {
      answerType = "Cards";
    }
    Chat.Chat_answer = JSON.stringify(answer);
    Chat.Chat_messageId = messageId == null ? "" : messageId;

    ActionFunction.HISTORY_RECORD.messages[1].type = "ai";
    ActionFunction.HISTORY_RECORD.messages[1].message =
      MessageController.TotalMessage_Synthesis;
    if (Chat.Chat_dataSource == "function" && answerType == "Text") {
      Chat.Chat_answerType = "gptText";
    } else if (Chat.Chat_dataSource === "web") {
      Chat.Chat_dataSource = "function";
      Chat.Chat_answerType = answerType;
    } else if (Chat.Chat_dataSource == "qa" && answerType == "External") {
      Chat.Chat_answer = JSON.stringify({ text: Text['notSupportformat'], type: "text" });
      Chat.Chat_answerType = "Text";
    } else {
      Chat.Chat_answerType = answerType;
    }
  },

  // 使用 fetch 和 blob 實現下載功能
  async downloadFile(url, fileName, fileType) {
    try {
      // 顯示下載中提示
      console.log(`開始下載: ${fileName}`);
      
      // 解析 URL
      const fullUrl = new URL(url, window.location.href).href;
      
      // 使用 fetch 獲取檔案
      const response = await fetch(fullUrl);
      
      if (!response.ok) {
        throw new Error(`下載失敗: ${response.status} ${response.statusText}`);
      }
      
      // 轉換為 blob
      const blob = await response.blob();
      
      // 創建 blob URL
      const blobUrl = URL.createObjectURL(blob);
      
      // 創建下載連結
      const link = document.createElement("a");
      document.body.appendChild(link);
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = "none";
      
      // 觸發下載
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
      
      console.log(`下載完成: ${fileName}`);
      
    } catch (error) {
      console.error("下載錯誤:", error);
      console.log("改用原本的下載方式...");
      
      // 降級到原本的 <a> 標籤下載方式
      try {
        const link = document.createElement("a");
        link.href = url;
        link.download = fileName;
        link.target = "_blank"; // 在新視窗開啟，避免頁面跳轉
        link.style.display = "none";
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log("原本下載方式成功");
      } catch (fallbackError) {
        console.error("原本下載方式也失敗:", fallbackError);
        alert(`下載失敗: ${error.message}`);
      }
    }
  },
};

var CodeParse = {
  isContainCode(content) {
    let regex = /```([\s\S] *?)```/g;
    return content.match(regex) != null;
  },

  getContentParse(content) {
    let regex = /```([\s\S] *?)```/g;
    let replacedString = content.replace(regex, "<pre><code>$1</code></pre>");
    return replacedString;
  },
};


