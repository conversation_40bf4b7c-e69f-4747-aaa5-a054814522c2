var QbiVideo = {
    TEMPLATE: `
        <div class="video-container">
            <div class="video-wrapper">
                <video class="video-player" preload="metadata" {poster}>
                    <source src="{videoUrl}" type="video/mp4">
                </video>
                <div class="video-overlay">
                    <div class="play-button">
                        <img class="play-icon" src="../../image/play-video.svg" alt="播放" />
                        <img class="pause-icon" src="../../image/pause-video.svg" alt="暫停" style="display: none;" />
                    </div>
                </div>
                <div class="video-controls">
                    <!-- <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div> -->
                    <div class="control-buttons">
                        <button class="fullscreen-btn" onclick="QbiVideo.toggleFullscreen(this)">
                            <svg viewBox="0 0 24 24" fill="white">
                                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `,

    create(answer) {
        let html = "";

        if (!!~answer.originalContentUrl.toLowerCase().indexOf("https://www.youtube.com")) {
            // YouTube 影片保持原有的 iframe 方式
            html = "<iframe class='videoIframe' src='" + answer.originalContentUrl + "'>" + "</iframe>";
            // YouTube 影片不提供下載功能
        } else {
            // 使用自定義播放器
            html = QbiVideo.TEMPLATE;
            html = html.replaceAll('{videoUrl}', answer.originalContentUrl);

            // 設置縮圖
            if (answer.thumbnailUrl) {
                html = html.replaceAll('{poster}', `poster="${answer.thumbnailUrl}"`);
            } else {
                html = html.replaceAll('{poster}', '');
            }

            // 設置下載信息
            answer.downloadHref = answer.originalContentUrl;
            answer.downloadName = QbiVideo.getDownloadName(answer.originalContentUrl);
        }

        return html;
    },

    /**
     * 從影片 URL 獲取下載檔名
     * @param {string} videoUrl - 影片URL
     * @returns {string} 檔名
     */
    getDownloadName(videoUrl) {
        try {
            let url = new URL(videoUrl);
            let pathname = url.pathname;
            let filename = pathname.split('/').pop();

            // 如果沒有副檔名，添加預設副檔名
            if (!filename.includes('.')) {
                filename += '.mp4';
            }

            return filename || 'video.mp4';
        } catch (e) {
            return 'video.mp4';
        }
    },

    /**
     * 切換播放/暫停
     */
    togglePlay(button) {
        const container = button.closest('.video-container');
        const video = container.querySelector('.video-player');
        const overlay = container.querySelector('.video-overlay');
        const playIcon = button.querySelector('.play-icon');
        const pauseIcon = button.querySelector('.pause-icon');
        const controls = container.querySelector('.video-controls');

        if (video && video.paused) {
            video.play().then(() => {
                if (playIcon) playIcon.style.display = 'none';
                if (pauseIcon) pauseIcon.style.display = 'block';
                overlay.classList.add('playing');
                if (controls) controls.classList.add('visible');
            }).catch(error => {
                console.error('Error playing video:', error);
            });

            // 設置進度更新
            // QbiVideo.updateProgress(container);
        } else if (video) {
            video.pause();
            if (playIcon) playIcon.style.display = 'block';
            if (pauseIcon) pauseIcon.style.display = 'none';
            overlay.classList.remove('playing');
        } else {
            console.error('Video element not found');
        }
    },

    /**
     * 更新播放進度
     */
    updateProgress(container) {
        const video = container.querySelector('.video-player');
        const progressFill = container.querySelector('.progress-fill');
        const timeDisplay = container.querySelector('.time-display');

        const updateTime = () => {
            if (!video.paused) {
                const progress = (video.currentTime / video.duration) * 100;
                progressFill.style.width = progress + '%';

                const currentMinutes = Math.floor(video.currentTime / 60);
                const currentSeconds = Math.floor(video.currentTime % 60);
                const durationMinutes = Math.floor(video.duration / 60);
                const durationSeconds = Math.floor(video.duration % 60);

                timeDisplay.textContent =
                    `${currentMinutes}:${currentSeconds.toString().padStart(2, '0')} / ` +
                    `${durationMinutes}:${durationSeconds.toString().padStart(2, '0')}`;

                requestAnimationFrame(updateTime);
            }
        };

        if (video.duration) {
            updateTime();
        } else {
            video.addEventListener('loadedmetadata', updateTime);
        }
    },

    /**
     * 切換全螢幕
     */
    toggleFullscreen(button) {
        const container = button.closest('.video-container');
        const video = container.querySelector('.video-player');

        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }
    },

    /**
     * 下載影片到本地
     * @param {string} videoUrl - 影片URL
     * @param {string} filename - 檔案名稱
     */
    async downloadVideo(videoUrl, filename) {
        try {
            const response = await fetch(videoUrl);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('下載影片時發生錯誤:', error);
            // 如果下載失敗，嘗試在新視窗開啟
            window.open(videoUrl, '_blank');
        }
    },

    /**
     * 初始化影片播放器事件監聽器
     */
    initVideoListeners() {
        // 使用事件委託來處理動態添加的影片播放器
        document.addEventListener('click', function (event) {
            // 處理 video-overlay 點擊播放/暫停（包括其內部的所有元素）
            if (event.target.classList.contains('video-overlay') ||
                event.target.closest('.video-overlay')) {

                // 防止事件冒泡到其他元素
                event.preventDefault();
                event.stopPropagation();

                const overlay = event.target.classList.contains('video-overlay') ?
                    event.target : event.target.closest('.video-overlay');
                const container = overlay.closest('.video-container');
                const playButton = container.querySelector('.play-button');

                QbiVideo.togglePlay(playButton);
                return;
            }

            // 處理影片本身點擊播放/暫停
            if (event.target.classList.contains('video-player')) {
                const container = event.target.closest('.video-container');
                const playButton = container.querySelector('.play-button');
                QbiVideo.togglePlay(playButton);
                return;
            }

            // 處理進度條點擊（但要排除在 video-overlay 內的情況）
            if ((event.target.classList.contains('progress-bar') ||
                event.target.closest('.progress-bar')) &&
                !event.target.closest('.video-overlay')) {

                const progressBar = event.target.classList.contains('progress-bar') ?
                    event.target : event.target.closest('.progress-bar');
                const container = progressBar.closest('.video-container');
                const video = container.querySelector('.video-player');

                const rect = progressBar.getBoundingClientRect();
                const clickX = event.clientX - rect.left;
                const width = rect.width;
                const clickTime = (clickX / width) * video.duration;

                video.currentTime = clickTime;
            }
        });

        // 處理影片結束事件
        document.addEventListener('ended', function (event) {
            if (event.target.classList.contains('video-player')) {
                const container = event.target.closest('.video-container');
                const overlay = container.querySelector('.video-overlay');
                const playButton = container.querySelector('.play-button');
                const playIcon = playButton.querySelector('.play-icon');
                const pauseIcon = playButton.querySelector('.pause-icon');

                // 重置播放按鈕狀態
                playIcon.style.display = 'block';
                pauseIcon.style.display = 'none';
                overlay.classList.remove('playing');

                // 重置進度條
                const progressFill = container.querySelector('.progress-fill');
                progressFill.style.width = '0%';
            }
        }, true);
    }
}

// 初始化事件監聽器
if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', QbiVideo.initVideoListeners);
    } else {
        QbiVideo.initVideoListeners();
    }
}