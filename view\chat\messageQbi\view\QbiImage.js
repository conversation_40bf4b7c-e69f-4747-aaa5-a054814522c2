var QbiImage = {

    TEMPLATE: `
        <a target="_blank" href="{href}">
            <img width="100%" src="{src}">
        </a>
    `,
    create(answer) {
        let html = QbiImage.TEMPLATE;
        let imageClickEnable = !CommonUtil.isStringEmpty(answer.imageClickUrl);
        let imageUrl = answer.imageUrl;
        let thumbnailUrl = (answer.thumbnailUrl == null || answer.thumbnailUrl == "") ? answer.imageUrl : answer.thumbnailUrl;
        html = html.replaceAll('{href}', imageClickEnable ? answer.imageClickUrl : imageUrl);
        html = html.replaceAll('{src}', thumbnailUrl);
        answer.downloadHref = imageUrl;
        answer.downloadName = QbiImage.getDownloadName(imageUrl);
        return html;
    },

    getDownloadName(imageUrl) {
        try {
            let url = new URL(imageUrl);
            let pathname = url.pathname;
            let filename = pathname.split('/').pop();
            if (!filename.includes('.')) {
                filename += '.jpg';
            }
            return filename || 'image.jpg';
        } catch (e) {
            return 'image.jpg';
        }
    },

    /**
     * 下載圖片到本地
     * @param {string} imageUrl - 圖片URL
     * @param {string} filename - 檔案名稱
     */
    downloadImage(imageUrl, filename) {
        try {
            // 創建一個隱藏的 canvas 來處理跨域圖片
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            // 設置跨域屬性
            img.crossOrigin = 'anonymous';

            img.onload = function () {
                // 設置 canvas 尺寸
                canvas.width = img.width;
                canvas.height = img.height;

                // 繪製圖片到 canvas
                ctx.drawImage(img, 0, 0);

                // 將 canvas 轉換為 blob
                canvas.toBlob(function (blob) {
                    if (blob) {
                        // 創建下載連結
                        const link = document.createElement('a');
                        const url = URL.createObjectURL(blob);
                        link.href = url;
                        link.download = filename;

                        // 觸發下載
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // 清理 URL 對象
                        URL.revokeObjectURL(url);
                    } else {
                        // 如果 blob 創建失敗，回退到直接下載
                        QbiImage.fallbackDownload(imageUrl, filename);
                    }
                }, 'image/jpeg', 0.9);
            };

            img.onerror = function () {
                // 如果圖片載入失敗，回退到直接下載
                QbiImage.fallbackDownload(imageUrl, filename);
            };
            // 開始載入圖片
            img.src = imageUrl;

        } catch (error) {
            console.error('下載圖片時發生錯誤:', error);
            // 發生錯誤時回退到直接下載
            QbiImage.fallbackDownload(imageUrl, filename);
        }
    },

    /**
     * 回退下載方法 - 直接使用 a 標籤下載
     * @param {string} imageUrl - 圖片URL
     * @param {string} filename - 檔案名稱
     */
    async fallbackDownload(imageUrl, filename) {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    },

    /**
     * 初始化下載按鈕事件監聽器
     */
    initDownloadListeners() {
        // 使用事件委託來處理動態添加的下載按鈕
        document.addEventListener('click', function (event) {
            if (event.target.classList.contains('satisfactionBtnDownload') &&
                event.target.hasAttribute('data-download-url')) {

                event.preventDefault();
                const downloadUrl = event.target.getAttribute('data-download-url');
                const filename = event.target.getAttribute('data-download-name');
                const downloadType = event.target.getAttribute('data-download-type');

                if (downloadUrl && filename) {
                    if (downloadType === 'Video') {
                        // 調用影片下載功能
                        if (typeof QbiVideo !== 'undefined' && QbiVideo.downloadVideo) {
                            QbiVideo.downloadVideo(downloadUrl, filename);
                        } else if (typeof QbiMediaCard !== 'undefined' && QbiMediaCard.downloadVideo) {
                            QbiMediaCard.downloadVideo(downloadUrl, filename);
                        } else {
                            // 回退到直接下載
                            QbiImage.fallbackDownload(downloadUrl, filename);
                        }
                    } else if (downloadType === 'File') {
                        // 檔案下載，使用 fallbackDownload 方法
                        QbiImage.fallbackDownload(downloadUrl, filename);
                    } else {
                        // 圖片下載
                        if (typeof QbiMediaCard !== 'undefined' && QbiMediaCard.downloadImage) {
                            QbiMediaCard.downloadImage(downloadUrl, filename);
                        } else {
                            QbiImage.downloadImage(downloadUrl, filename);
                        }
                    }
                }
            }
        });
    }
}

// 初始化事件監聽器
if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', QbiImage.initDownloadListeners);
    } else {
        QbiImage.initDownloadListeners();
    }
}
