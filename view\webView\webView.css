.closeIcon {
  width: 34px;
}
.webviewClose {
  position: fixed;
  right: 5px;
  cursor: pointer;
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 1.0;
}
.webviewback {
  width: 28px;
  position: fixed;
  left: 5px;
  cursor: pointer;
}
.webviewbackIcon {
  width: 28px;
}

.mainView {
  width: 100%;
  border: 1px solid #c8d2e0;
  padding: 10px;
  scrollbar-gutter: stable;
  overflow: auto;
  height: 95vh;
  background-color: #ffffff;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.mainWebView {
  height: 99.5vh;
  width: 100%;
  border: 0px;
}

.navBar {
  background-color: #1f4379;
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  z-index: 99999;
  border-bottom: 1px solid #c8d2e0;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 15px;
  color: white;
  font-weight: bold;
}

.mark {
  background-color: yellow;
}

body {
  overflow: hidden;
  background-color: #ffffff;
}

.question {
  color: rgb(226, 76, 75);
  font-size: 20px;
  font-weight: bold;
}

mark {
  background-color: #fff000 !important;
}
