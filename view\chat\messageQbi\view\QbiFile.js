var QbiFile = {
    create(answer) {
        var iconClass = QbiFile.getFileIconClass(answer.name);
        var hasUrl = answer.url != null;

        // 設置下載信息供 QbiAnswer.js 使用
        if (hasUrl) {
            answer.downloadHref = answer.url;
            answer.downloadName = answer.name;
        }

        var html =
            "<div class='ChatMessageContent ChatMessageFileContent'>" +
            "<div class=ChatMessageFileContentTop>" +
            "<div class='" +
            iconClass +
            "'></div>" +
            "<div class=ChatMessageFileContentName >" +
            answer.name +
            "</div>" +
            "<div class=ChatMessageFileContentInfo>" +
            Text['fileSize'] +
            "：" +
            "<label class=ChatMessageFileContentSize>" +
            QbiFile.formatFileSize(answer.size) +
            "</label>" +
            "<label class=ChatMessageFileContentStatus>" +
            (hasUrl ? "" : Text['UploadStart']) +
            "</label>" +
            "</div>" +
            "</div>" +
            "</div>";
        return html;
    },


    getFileIconClass(fileName) {
        var map = {
            Excel: /\.(xls|xlsx)$/i,
            Exe: /\.(exe|bat|cmd|sh|msi)$/i,
            Image: /\.(bmp|gif|jpg|jpeg|png)$/i,
            Ppt: /\.(ppt|pptx)$/i,
            Text: /\.(txt|log)$/i,
            Word: /\.(doc|docx)$/i,
            Zip: /\.(7z|cab|gz|iso|jar|rar|tar|z|zip)$/i,
        };
        for (var key in map) {
            if (map[key].test(fileName)) return "ChatMessageFileContentIcon ChatMessageFileContentIcon" + key;
        }
        return "ChatMessageFileContentIcon";
    },
    formatFileSize(size, removeRedundantZeros) {
        if (size == null) {
            return "";
        }
        var units = ["B", "KB", "MB", "GB", "TB", "PB"];
        removeRedundantZeros = removeRedundantZeros != false;
        for (var i = 0; i < units.length; ++i) {
            if (size < 10) {
                var s = QbiFile.formatNumber(size, "0.00");
                return (removeRedundantZeros ? s.replace(/\.00/, "") : s) + " " + units[i];
            } else if (size < 100) {
                var s = QbiFile.formatNumber(size, "0.0");
                return (removeRedundantZeros ? s.replace(/\.0/, "") : s) + " " + units[i];
            } else if (size < 1000 || i == units.length - 1) {
                return QbiFile.formatNumber(size, "0") + " " + units[i];
            } else {
                size = size / 1024;
            }
        }
    },

    formatNumber(value, format) {
        if (format == null) {
            return value.toString();
        }
        var buffer = [];
        var head = format.match(/[,0]*(?:\.|$)/)[0].replace(/[,\.]/g, "").length;
        var tail = format.match(/\.0*|$/)[0].replace(/\./, "").length;
        var group = format.match(/,[0#]*(?:\.|$)|$/)[0].replace(/[,\.]/g, "").length;
        var flag = value < 0 ? "-" : "";
        value = Math.round(Math.abs(value) * Math.pow(10, tail));
        for (var i = 0; i < tail; ++i, value = Math.floor(value / 10)) {
            buffer.push(value % 10);
        }
        if (tail > 0) {
            buffer.push(".");
        }
        for (var i = 0; i < head || value > 0; ++i, value = Math.floor(value / 10)) {
            buffer.push((value % 10) + (i > 0 && i % group == 0 ? "," : ""));
        }
        return flag + buffer.reverse().join("");
    },
}