var QbiCard = {
  TEMPLATE: `
    <div class="qbi-card-container" id="qbiCardContainer_{randomId}">
      <div class="qbi-card-wrapper" id="qbiCardWrapper_{randomId}">
        {CARDS_TEMPLATE}
      </div>
    </div>
  `,

  CARDS_TEMPLATE: `
    <div class="qbi-card-item">
      <div class="card">
        {IMAGE_TEMPLATE}
        <div class="card-body">
          <p class="card-title">{title}</p>
          <p class="card-text">{desc}</p>
          <ul class="list-group list-group-flush">
            {CARD_ITEM_TEMPLATE}
          </ul>
        </div>
      </div>
    </div>
  `,

  CARD_ITEM_TEMPLATE: `
       <li class="list-group-item cardItem"><button type="button" class="btn btn-link cardBtn" onclick="ChatEvent.onAnswerButtonClick('{option}','{code}')" title="{content}">{content}</button></li>
    `,
  KEYWORD_CARD_ITEM_TEMPLATE: `
       <li class="list-group-item cardItem"><button type="button" class="btn btn-link cardBtn" onclick="ChatEvent.KeyWordsendMessage('{content}')" title="{content}">{content}</button></li>
    `,

  IMAGE_TEMPLATE: `
        <img class="{QbiCardImage} {QbiCardstyle}"
        src="{ImageSrc}"
        alt="Card image cap" onclick="{imageClickUrl}">
    `,

  // 全域變數來追蹤拖曳狀態
  dragState: {
    isDown: false,
    startX: 0,
    scrollLeft: 0,
    currentContainer: null,
    currentWrapper: null
  },

  // 初始化全域拖曳事件監聽器（只執行一次）
  initGlobalDragScroll() {
    // 避免重複初始化
    if (QbiCard.globalDragInitialized) return;
    QbiCard.globalDragInitialized = true;

    // 使用事件委派，監聽所有 qbi-card-container 的拖曳事件
    document.addEventListener('mousedown', (e) => {
      const container = e.target.closest('.qbi-card-container');
      if (!container) return;

      const wrapper = container.querySelector('.qbi-card-wrapper');
      if (!wrapper) return;

      QbiCard.dragState.isDown = true;
      QbiCard.dragState.startX = e.clientX;
      QbiCard.dragState.scrollLeft = wrapper.scrollLeft;
      QbiCard.dragState.currentContainer = container;
      QbiCard.dragState.currentWrapper = wrapper;
      
      container.style.cursor = 'grabbing';
      e.preventDefault();
    });

    document.addEventListener('mouseleave', () => {
      if (QbiCard.dragState.isDown) {
        QbiCard.dragState.isDown = false;
        if (QbiCard.dragState.currentContainer) {
          QbiCard.dragState.currentContainer.style.cursor = 'grab';
        }
        QbiCard.dragState.currentContainer = null;
        QbiCard.dragState.currentWrapper = null;
      }
    });

    document.addEventListener('mouseup', () => {
      if (QbiCard.dragState.isDown) {
        QbiCard.dragState.isDown = false;
        if (QbiCard.dragState.currentContainer) {
          QbiCard.dragState.currentContainer.style.cursor = 'grab';
        }
        QbiCard.dragState.currentContainer = null;
        QbiCard.dragState.currentWrapper = null;
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!QbiCard.dragState.isDown || !QbiCard.dragState.currentWrapper) return;
      
      e.preventDefault();
      const x = e.clientX;
      const walk = (x - QbiCard.dragState.startX) * 1.5;
      QbiCard.dragState.currentWrapper.scrollLeft = QbiCard.dragState.scrollLeft - walk;
    });
  },

  create(answer) {
    let randomId = Math.random().toString(36).substr(2, 9);
    let html = QbiCard.TEMPLATE;
    let cards = answer.FQACardColumn;
    let cardsHTML = "";
    
    for (let i = 0; i < cards.length; i++) {
      let card = cards[i];

      // 卡片標頭圖片
      let imageHTML = "";
      let thumbnailImageUrl = card.thumbnailImageUrl;
      if (
        !CommonUtil.isStringEmpty(thumbnailImageUrl) ||
        !CommonUtil.isStringEmpty(card.imageClickUrl)
      ) {
        imageHTML = QbiCard.IMAGE_TEMPLATE;
        imageHTML = imageHTML.replaceAll("{ImageSrc}", thumbnailImageUrl);
        if (card.imageClickUrl) {
          imageHTML = imageHTML.replaceAll(
            "{imageClickUrl}",
            "window.open('" + card.imageClickUrl + "')"
          );
          imageHTML = imageHTML.replaceAll("{QbiCardstyle}", "pointer");
        } else {
          imageHTML = imageHTML.replaceAll("{imageClickUrl}", "");
          imageHTML = imageHTML.replaceAll("{QbiCardstyle}", "");
        }

        if (answer.imageAspectRatio == "rectangle") {
          imageHTML = imageHTML.replaceAll(
            "{QbiCardImage}",
            "QbiCardImage_rectangle"
          );
        } else {
          imageHTML = imageHTML.replaceAll(
            "{QbiCardImage}",
            "QbiCardImage_square"
          );
        }
      }

      // 卡片按鈕
      let cardButtons = card.FQACardAnswer;
      let cardButtonHTML = "";
      if (
        cardButtons[0] &&
        cardButtons[0].FCode &&
        cardButtons[0].FCode === "keyWord"
      ) {
        for (let j = 0; j < cardButtons.length; j++) {
          let card_showText = cardButtons[j].FShowText;
          let card_item = QbiCard.KEYWORD_CARD_ITEM_TEMPLATE;
          card_item = card_item.replaceAll("{content}", card_showText);
          cardButtonHTML += card_item;
        }
      } else {
        for (let j = 0; j < cardButtons.length; j++) {
          let card_showText = cardButtons[j].FShowText;
          let card_displayText = cardButtons[j].FDisplayText;
          let card_option = cardButtons[j].Option;
          let card_item = QbiCard.CARD_ITEM_TEMPLATE;
          let card_code = "";
          if (card_option == "Url") {
            card_code = encodeURIComponent(cardButtons[j].FName);
          } else if (card_option == "Option") {
            card_code = encodeURIComponent(JSON.stringify({
              FCode: cardButtons[j].FCode,
              FDisplayText: card_displayText,
              FShowText: card_showText
            }))
          } else {
            card_code = cardButtons[j].FCode;
          }
          card_item = card_item.replaceAll("{content}", card_showText);
          card_item = card_item.replaceAll("{code}", card_code);
          card_item = card_item.replaceAll("{option}", card_option);
          cardButtonHTML += card_item;
        }
      }

      // 卡片標題及副標題
      let title = card.title;
      let desc = card.FMsgAnswer;

      // 組裝單一卡片
      let cardHTML = QbiCard.CARDS_TEMPLATE;
      cardHTML = cardHTML.replaceAll("{title}", title);
      cardHTML = cardHTML.replaceAll("{desc}", desc);
      cardHTML = cardHTML.replaceAll("{IMAGE_TEMPLATE}", imageHTML);
      cardHTML = cardHTML.replaceAll("{CARD_ITEM_TEMPLATE}", cardButtonHTML);

      cardsHTML += cardHTML;
    }
    
    html = html.replaceAll("{CARDS_TEMPLATE}", cardsHTML);
    html = html.replaceAll("{randomId}", randomId);
    
    // 確保全域拖曳功能已初始化
    QbiCard.initGlobalDragScroll();
    
    return html;
  },
  
  // 保留原有的方法以向後相容，但現在使用全域事件委派
  initDragScroll(containerId) {
    // 這個方法現在只是為了向後相容，實際功能已經移到全域事件委派
    console.log('QbiCard: 使用全域事件委派處理拖曳滾動，無需單獨初始化');
  }
};
