var Chat = {
  DEBUG_MODE: false,
  SEND_MESSAGE_NOW_HEIGHT: 60,
  SEND_MESSAGE_DEFAULT_HEIGHT: 60,
  PARAMETER: {},
  DATA_KM: "DATA_KM",
  DATA_QA: "DATA_QA",
  isAutoSpeechSynthesis: false,
  isLastHistory: false,
  Chat_dataSource: "",
  Chat_answerType: "",
  Chat_answer: "",
  Chat_messageId: "",
  Chat_kmIds: [],
  Chat_chatRoomId: "",
  Chat_inputQuestion: "",
  Chat_showQuestion: "",
  Chat_isShowQuestion: true,
  Chat_ChanegeShowQuestion: false,
  Chat_HistoryTimestamp: null,
  Chat_HistoryMessageArray: [],
  doLoad() {
    if (Config.ENABLE_DEBUG) {
      var vConsole = new VConsole();
    }
    this.initApp();
    MessageController.doInit();
    CommonUtil.deleteLocalStorage(Chat.DATA_KM);
    CommonUtil.deleteLocalStorage(Chat.DATA_QA);
    Chat.currentFontSize = "14px";
  },
  initApp() {
    Chat.PageApp = new Vue({
      el: "#App",
      data: {
        T: Text,
        CatalogItems: [],
        CatalogSelects: [],
        FunctionItems: [],
        IS_CLEAR_TASK: true,
        KM_COPILOT_ID: parent.parent.EcpController.KM_COPILOT_ID,
        LOCK_SEND: false,
        NoticeContent: { content: null, noticeId: null },
        NowCatalog: { FId: "all", FName: Text["defaultCatalog"], hint: Text["defaultCatalog"] },
        NowSelectFunction: { functionName: Text["functionLoading"] },
        QbiCopilotInfo: { isTTSEnable: false, isSTTEnable: false, checkKeyWord: false, enableWebSearch: false },
        ServerConfig: { functionEnable: false },
        TOKEN_MAX: 1000,
        averageVolume: 0,
        historyList: [],
        inputQuestion: "",
        isKnowledgeFirst: true,
        isMicroPhoneTurn: false,
        isMicroSendIcon: true,
        isWebSearchForceEnable: Config.FORCE_WEB_SEARCH_ENABLE,
        knowledgeMode: "knowledge",
        mediaStream: null,
        roomId: "",
        showKnowledgeModeDropdown: false,
        showFunctionDropdown: false,
        siriWave: null,
        usermode: "",                
        isNewChat: true,
        showKMSelectDropdown: false,
      },
      filters: {
        truncate: function (value) {
          if (!value) return "";
          if (value.length <= 12) return value;
          return value.substring(0, 12) + "...";
        },
      },
      methods: {
        getTokensCount(content) {
          return gpt3encoder.countTokens(content);
        },
        sendMessage() {
          if (Chat.PageApp.inputQuestion.trim().length > 0) {
            Chat._sendMessage();
          }
        },
        cleanMessageAndShowGreeting() {
          Chat.cleanMessageAndShowGreeting();
        },
        WebSpeechRecognition() {
          WebSpeechRecognition.doLoad(event);
        },
        openCatalogView() {
          $("#catalogView").modal("toggle");
        },
        openKMSelectView() {
          Chat.PageApp.showKMSelectDropdown = !Chat.PageApp.showKMSelectDropdown;
        },
        openFunctionView() {
          Chat.PageApp.showFunctionDropdown = !Chat.PageApp.showFunctionDropdown;
        },
        closeFunctionDropdown() {
          Chat.PageApp.showFunctionDropdown = false;
        },
        choseCatalog(item) {
          ChatCataLog.choseCatalog(item);
        },
        doCataLogSelect(item) {
          ChatCataLog.doCataLogSelect(item);
        },
        _selectCataLog() {
          ChatCataLog._selectCataLog();
        },
        choseFunction(item) {
          Chat.PageApp.NowSelectFunction = item;
          setTimeout(() => {
            Chat.PageApp.showFunctionDropdown = false;
            // 計算 function 按鈕寬度並調整 km 按鈕位置
            this.adjustKMButtonPosition();
          }, 500);
          if (Chat.PageApp.IS_CLEAR_TASK) {
            MessageController.cleanChatMemory();
          }
        },
        adjustKMButtonPosition() {
          // 等待 DOM 更新完成
          this.$nextTick(() => {
            const functionButton = document.querySelector('.function-button-container');
            const kmSelectButton = document.querySelector('.km-select-button');
            
            if (functionButton && kmSelectButton) {
              const functionWidth = functionButton.offsetWidth;
              const baseWidth = 44; // 原始按鈕寬度
              const extraWidth = Math.max(0, functionWidth - baseWidth);
              
              // 設置 km 按鈕的 margin-left 來調整位置
              kmSelectButton.style.marginLeft = extraWidth > 0 ? `${extraWidth + 16}px` : '0px';
            }
          });
        },
        toggleKnowledgeModeDropdown() {
          if (!Chat.PageApp.ServerConfig.isCopilotPassGPT && !Chat.PageApp.ServerConfig.enableWebSearch) {
            return;
          }
          Chat.PageApp.showKnowledgeModeDropdown = !Chat.PageApp.showKnowledgeModeDropdown;
        },
        selectKnowledgeMode(mode) {
          Chat.PageApp.knowledgeMode = mode;
          Chat.PageApp.showKnowledgeModeDropdown = false;
          if (mode && !!mode) {
            if (mode == "knowledge") {
              Chat.PageApp.isKnowledgeFirst = true;
            } else if (mode == "web") {
              Chat.PageApp.isKnowledgeFirst = false;
            }
          }
          if (Chat.DEBUG_MODE) console.log("[chat.js] Knowledge mode selected", mode);
        },
      },
      mounted() {
        // 點擊外部關閉功能選單
        document.addEventListener('click', (event) => {
          const functionButton = document.querySelector('.function-button');
          const functionDropdown = document.querySelector('.function-dropdown');
          const kmSelectButton = document.querySelector('.km-select-button');
          const kmSelectDropdown = document.querySelector('.km-select-dropdown');
          const knowledgeModeBtn = document.querySelector('.knowledge-mode-btn');
          const knowledgeModeDropdown = document.querySelector('.knowledge-mode-dropdown');
          if (functionButton && functionDropdown) {
            if (!functionButton.contains(event.target) && !functionDropdown.contains(event.target)) {
              Chat.PageApp.showFunctionDropdown = false;
            }
          }
          if (kmSelectButton && kmSelectDropdown) {
            if (!kmSelectButton.contains(event.target) && !kmSelectDropdown.contains(event.target)) {
              Chat.PageApp.showKMSelectDropdown = false;
            }
          }
          if (knowledgeModeBtn && knowledgeModeDropdown) {
            if (!knowledgeModeBtn.contains(event.target) && !knowledgeModeDropdown.contains(event.target)) {
              Chat.PageApp.showKnowledgeModeDropdown = false;
            }
          }
          CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID, "hideAccountView", {});
        });

        // 初始化全域拖曳滾動功能
        setTimeout(() => {
          if (typeof QbiCard !== 'undefined' && QbiCard.initGlobalDragScroll) {
            QbiCard.initGlobalDragScroll();
          }
          if (typeof QbiAudio !== 'undefined' && QbiAudio.initGlobalDragSeek) {
            QbiAudio.initGlobalDragSeek();
          }
        }, 500);
      },
      watch: {
        inputQuestion: function (inputQuestion) {
          // Chat._doSetMessageBoxHeight(inputQuestion);
        },
        QbiCopilotInfo: function (QbiCopilotInfo) {
          document.getElementById("messageBox").style.height = "calc(100vh - 176.4px)";
          parent.Main.PageApp.showAutoSpeech = QbiCopilotInfo.isTTSEnable;
          Chat.PageApp.isMicroSendIcon = QbiCopilotInfo.isSTTEnable;
          Chat.PageApp.usermode = CommonUtil.getLocalStorage("UserInfo").mode;
          Chat.BeforeUnload();
        },
        LOCK_SEND(newVal, oldVal) {
          CommonUtil.send(parent.parent.EcpController.KM_COPILOT_ID, "watchChatLock", newVal);
        },
        NowSelectFunction: function(newVal, oldVal) {
          // 當選擇的功能改變時，調整 km 按鈕位置
          if (newVal && newVal.functionName !== oldVal?.functionName) {
            this.$nextTick(() => {
              this.adjustKMButtonPosition();
            });
          }
        },
        showFunctionDropdown: function(newVal, oldVal) {
          // 當 function 下拉選單展開/收合時，調整 km 按鈕位置
          this.$nextTick(() => {
            this.adjustKMButtonPosition();
          });
        },
      },
    });
    $("#App").show(100);
  },
  BeforeUnload() {
    if (Chat.PageApp.QbiCopilotInfo.isTTSEnable) {
      window.addEventListener("beforeunload", function (event) {
        Chat.speakingCancel();
      });
    }
    window.addEventListener("beforeunload", function (event) {
      const data = JSON.stringify({ roomId: Chat.Chat_chatRoomId });
      navigator.sendBeacon(Config.ECP_URL + "/openapi/copilot/stopAIRoom", data);
    });
  },
  changeFontSize(size) {
    const fontSize = size === "l" ? "18px" : size === "m" ? "16px" : "14px";
    document.getElementById("messageList").style.fontSize = fontSize;
    const messageElements = document.querySelectorAll(".MessageText");
    messageElements.forEach((element) => {
      element.style.fontSize = fontSize;
    });
    const fontElements = document.querySelectorAll("#messageList .MessageText font");
    fontElements.forEach((element) => {
      element.style.fontSize = fontSize;
    });
    Chat.currentFontSize = fontSize;
  },
  disPlayMode(stringType) {
    if (Chat.PageApp.QbiCopilotInfo.isSTTEnable) {
      if (stringType == "siriWaveStart") {
        Chat.PageApp.siriWave = new SiriWave({ container: document.getElementById("siri-container"), speed: 0.25, height: 50, amplitude: 0.08 });
        Chat.PageApp.siriWave.color = "48, 126, 218";
      } else if (stringType.startsWith("SiriWaveDuring")) {
        if (stringType == "SiriWaveDuring_Start") {
          Chat.PageApp.siriWave.amplitude = 1.1;
        } else {
          Chat.PageApp.siriWave.amplitude = 0.4;
        }
      } else if (stringType == "siriWaveEnd") {
        Chat.PageApp.siriWave.dispose();
      }
    }
  },
  onMessageBoxPress(event) {
    event = event || window.event;
    if (event.keyCode === 13 && !event.shiftKey) {
      event.returnValue = false;
      Chat._sendMessage();
      return false;
    }
  },
  onMessageBoxUp(event) {
    const sendMessageBox = document.getElementById("sendMessageBox");
    if (Chat.PageApp.QbiCopilotInfo.isSTTEnable) {
      if (sendMessageBox.value.trim() != "") {
        Chat.PageApp.isMicroSendIcon = false;
      } else {
        Chat.PageApp.isMicroSendIcon = true;
      }
    }
  },
  setSpeechDisPlay(mode) {
    const sendMessageBox = document.querySelector("#sendMessageBox");
    switch (mode) {
      case "init":
        Chat.PageApp.isMicroPhoneTurn = true;
        sendMessageBox.disabled = true;
        break;
      case "recognizing":
        if (Chat.PageApp.isMicroPhoneTurn) {
          Chat.PageApp.siriWave.dispose();
          Chat.PageApp.isMicroPhoneTurn = false;
          sendMessageBox.disabled = false;
        } else {
          Chat.PageApp.isMicroPhoneTurn = true;
          sendMessageBox.disabled = true;
          Chat.disPlayMode("siriWaveStart");
        }
        break;
      case "end":
        Chat.PageApp.isMicroPhoneTurn = false;
        sendMessageBox.disabled = false;
        Chat.disPlayMode("siriWaveEnd");
        break;
    }
  },
  doGetHistoryData(data) {
    if (Chat.PageApp.historyList.find((item) => item.FId === Chat.Chat_chatRoomId)?.isNewStartChat) {
      ChatEvent.stopChat().then((res) => {
        Chat.cleanMessage().then((result) => {
          if (Chat.Chat_chatRoomId == "" || Chat.Chat_chatRoomId == null) {
            CommonUtil.send(Chat.PageApp.KM_COPILOT_ID, "doGetHistoryData", data);
            return;
          } else {
            CommonUtil.send(Chat.PageApp.KM_COPILOT_ID, "doGetHistoryData", data);
          }
        });
      });
    } else {
      Chat.cleanMessage().then((result) => {
        if (Chat.Chat_chatRoomId == "" || Chat.Chat_chatRoomId == null) {
          CommonUtil.send(Chat.PageApp.KM_COPILOT_ID, "doGetHistoryData", data);
          return;
        } else {
          CommonUtil.send(Chat.PageApp.KM_COPILOT_ID, "doGetHistoryData", data);
        }
      });
    }
  },
  doShowHistoryData(data) {
    Chat.Chat_chatRoomId = data.chatRoomId;
    if (data.historyData?.length > 0) {
      document.getElementById("messageList").innerHTML = data.historyData;
      MessageController.COUNT = ChatEvent.getLatestMessageIndexFromHTML(data.historyData) + 1;
      if (Chat.currentFontSize) {
        const messageElements = document.querySelectorAll(".MessageText");
        messageElements.forEach((element) => {
          element.style.fontSize = Chat.currentFontSize;
        });
        const fontElements = document.querySelectorAll("#messageList .MessageText font");
        fontElements.forEach((element) => {
          element.style.fontSize = Chat.currentFontSize;
        });
      }
      MessageController._goChatBottom();
      
      // 確保歷史紀錄載入後拖曳功能正常運作
      setTimeout(() => {
        if (typeof QbiCard !== 'undefined' && QbiCard.initGlobalDragScroll) {
          QbiCard.initGlobalDragScroll();
        }
        if (typeof QbiAudio !== 'undefined' && QbiAudio.initGlobalDragSeek) {
          QbiAudio.initGlobalDragSeek();
        }
      }, 100);
    }
    if (data.chat_history_list?.length > 0) {
      MessageController.doHandleHistoryToAddChatHistory(data.chat_history_list);
    }
  },
  cleanMessageAndShowGreeting() {
    Chat.cleanMessage().then((res) => {
      Chat.PageApp.isNewChat = true;
      MessageController.doShowGreeting();
    });
  },
  cleanMessage() {
    return new Promise((resolve, reject) => {
      if (Chat.DEBUG_MODE) console.log("[chat.js] LOCK_SEND", Chat.PageApp.LOCK_SEND);
      if (Chat.PageApp.LOCK_SEND) {
        return;
      }
      ChatEvent.SatisfactionRecord = [];
      Chat.PageApp.inputQuestion = "";
      Chat.Chat_chatRoomId = "";
      CommonUtil.send(Chat.PageApp.KM_COPILOT_ID, "cleanhistoryNowId", {});
      MessageController.cleanMessage();
      Chat.speakingCancel();
      ChatEvent.cleanChatRecord();
      QbiQuickReply.closeQuickReplyPool();
      parent.parent.EcpController.doWebViewToggle(true);
      Chat.PageApp.LOCK_SEND = false;
      resolve();
    });
  },
  sttSendMessage(sttWord) {
    const sendMessageBox = document.querySelector("#sendMessageBox");
    sendMessageBox.disabled = false;
    Chat.PageApp.isMicroPhoneTurn = false;
    Chat.PageApp.inputQuestion = sttWord;
    sendMessageBox.disabled = true;
    Chat.PageApp.isMicroPhoneTurn = true;
    Chat._sendMessage();
  },
  speakingCancel() {
    if (WebSpeechSynthesis.synth.speaking) {
      WebSpeechSynthesis.synth.cancel();
    }
  },
  _sendMessage() {
    let questionLength = Chat.PageApp.inputQuestion.replace(/ |　/g, '').length;
    if (Chat.PageApp.LOCK_SEND || questionLength===0) {
        return;
    }
    if (Chat.Chat_chatRoomId==="" && Chat.PageApp.isNewChat) {
        ChatEvent.startChat().then((data) => {
            Chat.PageApp.isNewChat = false;
            Chat._sendMessage();
            return;
        });
    }
    Chat.speakingCancel();
    ChatEvent.cleanChatRecord();
    QbiQuickReply.closeQuickReplyPool();
    let token = gpt3encoder.countTokens(Chat.PageApp.inputQuestion);
    if (questionLength > 0 && token <= Chat.PageApp.TOKEN_MAX) {
      try {
        Chat.PageApp.LOCK_SEND = true;
        Chat.Chat_inputQuestion = Chat.PageApp.inputQuestion.replace(/\r\n|\n|\/|\\|"/g, "");
        let originalQuestion = Chat.PageApp.inputQuestion.replace(/\r\n|\n|\/|\\|"/g, "");
        let inputQuestion = Chat.PageApp.inputQuestion.replace(/\r\n|\n/g, "<br>");
        let content = MessageController.text(inputQuestion);
        if (Chat.Chat_isShowQuestion) {
          MessageController.doAddMessage(content, MessageController.RIGHT);
        }
        Chat.PageApp.inputQuestion = "";
        // 一般Copilot問答流程
        let args = {
          login: UserInfo.getData().mode,
          chatId: UserInfo.getData().userId,
          inputQuestion: originalQuestion,
        };
        // @使用直通流程
        if (args.inputQuestion.startsWith("@") || args.inputQuestion.startsWith("＠") || Chat.PageApp.knowledgeMode == "gpt") {
          if (Chat.DEBUG_MODE) console.log("[chat.js] get_km_answer");
          if(Chat.PageApp.knowledgeMode == "gpt" && !(args.inputQuestion.startsWith("@") || args.inputQuestion.startsWith("＠"))){
            args.inputQuestion = "@" + args.inputQuestion;
          }
          ActionManager.get_km_answer(args);
          // 是否有開啟Function call流程
        } else if (!Chat.PageApp.isKnowledgeFirst && ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE)) {
          if (Chat.DEBUG_MODE) console.log("[chat.js] get_km_answer");
          ActionManager.get_km_answer(args);
        } else if (Chat.PageApp.ServerConfig.functionEnable) {
          if (Chat.DEBUG_MODE) console.log("[chat.js] do_function");
          ActionManager.do_function(args);
          // 啟用混合式流程
        } else {
          let answerMode = Chat.PageApp.ServerConfig.answerMode;
          if (ActionCopilot.ANSWER_GPT_AND_KM == answerMode) {
            if (Chat.DEBUG_MODE) console.log("[chat.js] do_QbiBotPlus");
            ActionManager.do_QbiBotPlus(args);
          } else if (ActionCopilot.ANSWER_GPT_PLUS_EASY == answerMode) {
            if (Chat.DEBUG_MODE) console.log("[chat.js] do_QbiBotPlus");
            ActionManager.do_QbiBotPlus(args);
          } else {
            if (Chat.DEBUG_MODE) console.log("[chat.js] get_km_answer");
            ActionManager.get_km_answer(args);
          }
        }
      } catch (e) {
        console.error("[Chat _sendMessage ]" + e);
        ChatEvent._showError();
      }
    }
    setTimeout(() => {
      Chat.onMessageBoxUp();
    }, 100);
  },
  _doSetMessageBoxHeight(inputQuestion) {
    if (gpt3encoder.countTokens(inputQuestion) >= Chat.PageApp.TOKEN_MAX) {
      return;
    }
    let id = "sendMessageBox";
    let height = document.getElementById(id).scrollHeight;
    if (height != Chat.SEND_MESSAGE_NOW_HEIGHT) {
      let messageListHeight = document.getElementById("messageList").clientHeight;
      if (height < messageListHeight - 100) {
        $("#sendMessageBox").animate({ height: height }, 200);
        Chat.SEND_MESSAGE_NOW_HEIGHT = height;
      }
    }
    if (inputQuestion.length == 0) {
      $("#sendMessageBox").animate({ height: 60 }, 400);
      Chat.SEND_MESSAGE_NOW_HEIGHT = Chat.SEND_MESSAGE_DEFAULT_HEIGHT;
    }
  },
};
