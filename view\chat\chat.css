body {
  overflow-y: hidden;
  /* Hide vertical scrollbar */
  overflow-x: hidden;
  /* Hide horizontal scrollbar */
  font-size: 14px;
  background-color: #ffffff;
}

.messageBox {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.messageList {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-gutter: stable;
  z-index: 1;
}

#ToolZone {
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 40px;
  max-height: 85px;
  z-index: 2;
}

.catalog {
  width: 100%;
  background-color: rgb(198, 215, 255);
  text-align: center;
  cursor: pointer;
}

.catalogItem {
  background-color: #497cc7;
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.catalogItems {
  overflow: scroll;
  height: 50vh;
}

.catalogSelect {
  cursor: pointer;
  color: rgb(68, 130, 196);
  user-select: none;
}

.catalogText {
  font-size: 1rem;
}

.catalogContent {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 1rem;
}

.catalogOK {
  align-items: center;
  background-color: #497cc7;
  border-radius: 0.3rem;
  border: 1px solid #ccc;
  cursor: pointer;
  display: inline-flex;
  font-size: 13px;
  gap: 6px;
  padding: 8px 12px;
  vertical-align: middle;
  width: 102px;
}

.catalogOK svg {
  fill: currentColor;
  height: 1em;
  width: 2em;
}

.sendMessage {
  position: fixed;
  bottom: 0px;
  width: 100%;
  background-color: rgb(139, 168, 217);
  z-index: 3;
}

.sendMessageDiv {
  margin: 0px 10px;
  border-radius: 6px;
  display: flex;
  background-color: rgb(255, 255, 255);
  border-radius: 6px;
  border: rgb(193 193 193) solid 1px;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0px;
  position: relative;
  width: 100%;
  min-height: 1px;
  transition:
    width 1s,
    height 1s;
}

#sendMessageBox {
  border: 3px;
  outline: none;
  width: calc(100% - 1.7rem);
  border-radius: 5px;
  resize: none; 
  overflow-y: auto; 
  height: 100%;
  width: 100%;
}

.MainsendMessageBox {
  position: unset;
  flex: 1;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  bottom: 1px;
  left: -21.5px;
  width: 93.56%;
}

.MainIconBox {
  width: 5%;
}

/* --------------麥克風圖示-------------- */
.microPhoneIconOn {
  position: unset;
  right: 10px;
  bottom: 10px;
  cursor: pointer;
  border-radius: 50%;
  transition:
    width 0.1s,
    height 0.1s;
}

/* .microPhoneIconOn filter removed - only red border for indication */

.microPhoneIconOff {
  position: unset;
  right: 10px;
  bottom: 10px;
  cursor: pointer;
  border: none;
  transition:
    width 0.1s,
    height 0.1s;
}

/* --------------發送圖示-------------- */

.sendIcon {
  position: unset;
  cursor: pointer;
  width: 38px;
}

/* --------------外部搜尋圖示-------------- */
.knowledgeIconOn {
  width: 30px;
  margin-bottom: 5px;
  position: absolute;
  bottom: 0px;
  left: 5px;
  cursor: pointer;
  transition:
    width 0.1s,
    height 0.1s;
}

.knowledgeIconOff {
  width: 30px;
  margin-bottom: 5px;
  position: absolute;
  bottom: 0px;
  left: 5px;
  cursor: pointer;
  transition:
    width 0.1s,
    height 0.1s;
}

/* --------------清除主題圖示-------------- */

.iconcontroly {
  display: inline-grid;
}

.cleanIcon {
  height: 35.34px;
  width: 38px;
  cursor: pointer;
  transition:
    width 0.1s,
    height 0.1s;
}

/* --------------發問任務圖示-------------- */
.catalogBtnUp {
  margin-bottom: 3px;
}

.catalogBtnDown {
  margin-bottom: 3px;
}

.ChatMessageTime {
  color: #888;
  font-size: 10px;
}

/* 新版聊天氣泡樣式 */
.chat-message {
  margin-bottom: 16px;
}

.chat-bubble-group {
  padding-bottom: 30px;
}

.chat-bubble {
  display: inline-block;
  max-width: 60%;
  padding: 10px 12px;
  border-radius: 10px;
  font-size: 14px;
  word-break: break-word;
}

.chat-bubble.bot {
  float: left;
  background-color: #f1f1f1;
  text-align: left;
}

.chat-time.bot {
  float: left;
  font-size: 10px;
  color: #888;
  margin-top: 25px;
}

.chat-bubble.user {
  float: right;
  background-color: #d3ecff;
  margin-left: auto;
  text-align: right;
}

.chat-time.user {
  float: right;
  font-size: 10px;
  color: #888;
  margin-top: 25px;
}

.sendToken {
  color: white;
  padding-left: 25px;
}

.functionTitle {
  font-size: 1rem;
  color: white;
  transition:
    width 0.1s,
    height 0.1s;
}

/* 
.functionButton {
    margin-bottom: 5px;
    position: absolute;
    right: 2px;
    transition: width 0.1s, height 0.1s;
} */

.functionButton:hover {
  width: 35px;
}

.tokenText {
  position: unset;
  margin-right: 10px;
}

.audio-wave {
  position: absolute;
  height: 0px;
  margin-left: 13%;
  width: 65%;
  bottom: 70px;
  transition:
    width 0.1s,
    height 0.1s;
}

/*自定義*/
.SendBox {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  margin: 5px 0px;
}

/*自定義*/
.message-info-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 100%;
  padding: 5px;
}

/* 新版聊天輸入區樣式 */
.chat-input {
  border-top: 1px solid #ccc;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: #ffffff;
}

.chat-input-row1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 75px;
}

.chat-input-row1 input {
  flex: 1;
  padding: 8px 10px;
  font-size: 14px;
  border: none;
  outline: none;
  height: 75px;
  width: 400px;
  background: transparent;
}

.chat-input-row1 .input-right {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-right: -3px;
  align-items: center;
  justify-content: space-between;
  color: #888;
  height: 90%;
  font-size: 12px;
}

.chat-input-row2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px;
  /* padding: 6px 1px; */
}

.chat-actions-left {
  display: flex;
  gap: 12px;
}

.chat-actions-left span {
  cursor: pointer;
}

.chat-actions-left-icon {
  width: 34px;
  height: 34px;
}

.chat-actions-left-km-icon {
  width: 12px;
  height: 12px;
}

.function-button:hover circle {
  stroke: #1f4379;
}

.km-select-button:hover circle {
  stroke: #1f4379;
}

.chat-actions-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

.chat-actions-right select {
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
}

.chat-actions-right span {
  /* background: #ffffff; */
  /* border: none; */
  /* border-radius: 50%; */
  /* width: 30px;
  height: 30px; */
  /* color: white;
  font-size: 18px; */
  cursor: pointer;
}

.chat-actions-right button svg {
  width: 28px;
  height: 28px;
  padding-right: 2px;
  padding-bottom: 3px;
}

/* 響應式設計 */
@media screen and (max-width: 768px) {
  .chat-bubble {
    max-width: calc(133.33%);
    /* 4/3 of container width */
  }
}

.knowledge-mode-container {
  position: relative;
  display: inline-block;
}

.knowledge-mode-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  border: 1px solid #ddd;
  background: white;
  font-size: 14px;
  outline: none;
  padding: 6px 14px;
  border-radius: 40px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  min-width: 108px;
  justify-content: center;
}

.knowledge-mode-btn.active {
  border-color: #666;
  box-shadow: 0 0 0 2px rgba(49, 130, 246, 0.1);
}

.knowledge-mode-text {
  flex: 1;
}

.dropdown-arrow {
  transition: transform 0.2s ease;
}

.knowledge-mode-btn.active .dropdown-arrow {
  transform: rotate(180deg);
}

.knowledge-mode-dropdown {
  position: absolute;
  /* right: 0; */
  z-index: 1001;
  min-width: 110px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.knowledge-mode-dropdown.dropup {
  bottom: 35px;
  /* 往上顯示 */
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  /* 往上的陰影 */
}

.knowledge-mode-menu-items {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #666;
  border-radius: 15px;
  box-shadow: 0 0 0 2px rgba(49, 130, 246, 0.1);
}

.knowledge-mode-option {
  padding: 6px 14px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #D9D9D9;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.knowledge-mode-option:first-child {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.knowledge-mode-option:last-child {
  border-bottom: none;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.knowledge-mode-option:hover {
  border-radius: 10px;
  background-color: #e6e6e6;
}

.knowledge-mode-option.selected {
  /* background-color: #666; */
  color: white;
}

/* .knowledge-mode-option.selected:hover {
  background-color: #1f4379;
} */

.swal2-styled.swal2-confirm {
  background-color: #497cc7;
}

/* 自定義功能選單樣式 */
.function-dropdown {
  position: absolute;
  bottom: 100%;
  left: 10px;
  margin-bottom: 8px;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  max-height: 240px;
  overflow: auto;
}

.function-menu-items {
  width: 100%;
}

.function-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #D9D9D9;
  transition: background-color 0.2s ease;
}

.function-option:last-child {
  margin-bottom: 0;
}

.function-option:hover {
  background-color: #f5f5f5;
}

.function-option-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.function-option-text {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  line-height: 1.4;
}

/* 功能按鈕容器相對定位 */
.chat-actions-left {
  position: relative;
}

/* 知識目錄選單樣式 */
.km-select-dropdown {
  position: absolute;
  bottom: 100%;
  left: 50px;
  margin-bottom: 8px;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 240px;
  height: 300px;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  align-items: flex-start;
}

.km-select-menu-items {
  width: 100%;
}

.km-breadcrumb {
  display: flex;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
}

.breadcrumb-item {
  cursor: pointer;
  color: #1890FF;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #40a9ff;
}

.breadcrumb-separator {
  margin: 0 4px;
  color: #999;
}

.km-content {
  padding: 10px 3px;
  max-height: 300px;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
  overflow-y: auto;
}

.km-message {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
  font-size: 14px;
}

.km-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #D9D9D9;
  transition: background-color 0.2s ease;
}

.km-option:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.km-breadcrumb-arrow {
  padding: 15px 15px 10px 15px;
}

.km-option:hover {
  background-color: #f5f5f5;
}

.km-option-text {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  line-height: 1.4;
}

.km-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.km-btn {
  padding: 6px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.km-btn-confirm {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.km-btn-confirm:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.dispearOutline {
  outline: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 功能按鈕展開動畫樣式 */
.function-button-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 17px;
  padding: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  width: 34px;
  height: 34px;
  border: 1px solid #e0e0e0;
}

.function-button-container.expanded {
  width: fit-content;
  height: 34px;
  padding-right: 10px;
  background: #1F4379;
  border-radius: 40px;
  z-index: 1;
  transition: all 0.3s ease;
}

.function-button-container:not(.expanded) {
  background: white;
}

.function-button-container.expanded svg .s1 {
  stroke: white;
}

.function-button-container:not(.expanded) svg .s1 {
  stroke: black;
}

.function-button-container.expanded svg .s2 {
  fill: white;
}

.function-button-container:not(.expanded) svg .s2 {
  fill: black;
}

.function-button-container.expanded svg .s3 {
  fill: #1F4379;
}

.function-button-container:not(.expanded) svg .s3 {
  fill: white;
}

.function-icon {
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.function-text-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  margin-left: 0;
}

.function-button-container.expanded .function-text-container {
  opacity: 1;
  transform: translateX(0);
  margin-left: 3px;
  justify-content: flex-start;
}

.function-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 知識目錄按鈕展開動畫樣式 */
.km-select-button-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 17px;
  padding: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  width: 34px;
  height: 34px;
  border: 1px solid #e0e0e0;
}

.km-select-button {
  transition: margin-left 0.3s ease;
}

.km-select-button-container.expanded {
  width: fit-content;
  height: 34px;
  padding-right: 10px;
  background: #1F4379;
  border-radius: 40px;
  transition: all 0.3s ease;
}

.km-select-button-container.expanded svg .s1 {
  stroke: white;
}

.km-select-button-container:not(.expanded) svg .s1 {
  stroke: black;
}

.km-select-button-container.expanded svg .s2 {
  fill: #1F4379;
}

.km-select-button-container:not(.expanded) svg .s2 {
  fill: white;
}

.km-select-icon {
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.km-select-text-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  margin-left: 0;
}

.km-select-button-container.expanded .km-select-text-container {
  opacity: 1;
  transform: translateX(0);
  margin-left: 3px;
  justify-content: flex-start;
}

.km-select-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}