.QbiAnswer_Container {
}

.Multiple_Container {
    display: table-caption;
    width: 95%;
}

.MessageCard {
    background-color: transparent  ;
    padding: 10px  ;
}

.MessageCard .ViewMoreText {
    float: inline-end;
    padding: 3px;
    border-radius: 3px;
    margin-right: 20px;
}

/* Card styling - 讓卡片填滿氣泡訊息空間 */
.MessageCard .card {
    margin: 0  ;
    width: 92%  ;
    border-radius: 10px;
    padding: 10px  ;
}

.card-title{
    color: var(--FONT-normal, #000);
    font-family: "SF Pro";
    font-size: 16px;
    font-style: normal;
    font-weight: 590;
    line-height: 19px;
}

.card-text{
    color: var(--FONT-subtitle, #838383);
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

/* MessageCard with Timestamp Container */
.MessageText.MessageCard_WithTimestamp {
    display: flex;
    align-items: flex-end;
    /* gap: 8px; */
    margin-bottom: 15px;
}

/* MessageCard Timestamp styling */
.MessageText.MessageCard_Timestamp {
    color: #666;
    white-space: nowrap;
    align-self: flex-end;
    margin-bottom: -10px;
    margin-left: -5px;
}

.QbiCardImage_square {
    width: 100%;
}

.QbiCardImage_rectangle {
    height: 200px;
    width: 100%;
}

.WordBreakAll {
    word-break: break-all;
}

.mySwiper {
    --swiper-navigation-color: #8ba8d9;
    --swiper-pagination-color: #8ba8d9;
}

.cardItem {
    padding: 0px;
}

.cardBtn {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/*-- 影片 --*/
.videoIframe {
    width: 100%;
    height: auto;
}

/* 自定義影片播放器樣式 */
.video-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    /* 16:9 比例 */
}

.video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.video-overlay.playing {
    opacity: 0;
    pointer-events: none;
}

.video-overlay:hover {
    opacity: 1  ;
    pointer-events: auto  ;
}

.play-button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    border-color: white;
    transform: scale(1.1);
}

.play-button img {
    width: 32px;
    height: 32px;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 20px 15px 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-controls.visible,
.video-container:hover .video-controls {
    opacity: 1;
}

.progress-container {
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: #007bff;
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
}

.control-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.time-display {
    color: white;
    font-size: 14px;
    font-family: monospace;
}

.fullscreen-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.2s ease;
}

.fullscreen-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.fullscreen-btn svg {
    width: 20px;
    height: 20px;
}


/*-- ChatMessageFileContent --*/

.ChatMessageFileContent {
    width: 260px;
    line-height: 20px;
}

.ChatMessageFileContentTop {
    /* border-bottom: 1px solid #d3d3d3; */
    position: relative;
    padding: 6px 6px 4px 50px;
}

.ChatMessageFileContentBottom {
    padding: 3px 10px;
    text-align: right;
}

.ChatMessageFileContentIcon {
    position: absolute;
    width: 32px;
    height: 32px;
    left: 10px;
    top: 9px;
    background-image: url(../../../../../image/FileTypeUnknown.png);
}

.ChatMessageFileContentIconExcel {
    background-image: url(../../../../../image/FileTypeExcel.svg)  ;
    background-repeat: no-repeat;
    background-size: 32px 32px;
    background-position: center;
}

.ChatMessageFileContentIconExe {
    background-image: url(../../../../../image/FileTypeExe.png);
}

.ChatMessageFileContentIconImage {
    background-image: url(../../../../../image/FileTypeImage.png);
}

.ChatMessageFileContentIconPpt {
    background-image: url(../../../../../image/FileTypePpt.svg)  ;
    background-repeat: no-repeat;
    background-size: 32px 32px;
    background-position: center;
}

.ChatMessageFileContentIconText {
    background-image: url(../../../../../image/FileTypeText.png);
}

.ChatMessageFileContentIconWord {
    background-image: url(../../../../../image/FileTypeWord.svg)  ;
    background-repeat: no-repeat;
    background-size: 32px 32px;
    background-position: center;
}

.ChatMessageFileContentIconZip {
    background-image: url(../../../../../image/FileTypeZip.png);
}

.ChatMessageFileContentName {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ChatMessageFileContentInfo {
    color: #808080;
}

.ChatMessageFileContentStatus {
    padding-left: 16px;
}

.pointer {
    cursor: pointer
}

/*QuickReply*/

.QuickreplySlide {
    padding: 2px;
    margin: 3px 1px;
}

.QuickReply_div {
    border-radius: 20px;
    background: #468FDE;
    display: flex;
    height: 32px;
    padding: 0 10px;
    align-items: center;
    color: #FFF;
    font-family: "SF Pro";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 36px;
    cursor: pointer;
    transition: background-color 0.1s;
}

.QuickReply_img {
    display: block;
    float: left;
    width: 30px;
    height: 30px;
    padding: 1px;
    border-radius: 60px;
    background-color: white;
}

#QuickReply_Container {
    margin-left: 5px  ;
    width: 100%;
}

.QuickReply_Vertical {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    height: 100%;
}

/* QuickReply */
.QuickReply {
    border: rgb(180, 201, 232) solid;
    background-color: rgb(227, 239, 255);
    border-radius: 8px;
    padding: 5px;
    text-align: left;
    word-wrap: break-word;
    display: inline-block;
    margin: 5px;
    cursor: pointer;
    transition: background-color 0.1s;
}

/*midiaCard*/

.cardImage {
    border-radius: 10px 10px 0px 0px;
    width: 238px;
}

.divCenter {
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

.swiper_ul {
    list-style: none;
    text-align: center;
    display: block;
    padding: 0px;
    margin: 0;
    border-top: 1px solid #d1d1d1;
}

.swiper_ul>.swiper_li:last-child {
    padding-bottom: 8px;
}

.swiper_ul>.swiper_li {
    background-color: #fff;
    display: block;
    height: auto;
    overflow: visible;
    padding: 7px;
    margin: 0;
    color: #365899;
}

.swiper_ul>.swiper_li:last-child {
    border-radius: 0px 0px 10px 10px;
}

.swiper_ul>.swiper_li:hover {
    background-color: #d1d1d1;
    color: #365899;
    cursor: pointer;
}

.cardsSlide {
    border: 1px solid #d1d1d1  ;
    border-radius: 10px;
    margin-right: 20px;
}

.audio-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* padding: 15px; */
    /* background: #f5f5f5; */
    border-radius: 10px;
    margin: 10px 0;
}

.audio-player {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
}

.sound-wave {
    display: flex;
    align-items: center;
    height: 40px;
    flex: 1;
    justify-content: center;
    position: relative;
    border-radius: 4px;
    outline: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 移除進度覆蓋層，只使用波形條本身的顏色變化 */

.sound-wave .sound-bar {
    width: 1px;
    height: 3px;
    background-color: #000000;
    margin-right: 1px;
    transition: background-color 0.3s ease;
}



/* 波形條默認顏色 - 白色 */
.sound-wave .sound-bar {
    background-color: #9a9a9a;
}

/* 靜態波形條 - 不同高度營造視覺層次 */
.sound-wave .sound-bar:nth-child(3n) {
    height: 8px;
}

.sound-wave .sound-bar:nth-child(5n) {
    height: 12px;
}

.sound-wave .sound-bar:nth-child(7n) {
    height: 6px;
}

.sound-wave .sound-bar:nth-child(11n) {
    height: 16px;
}

.sound-wave .sound-bar:nth-child(13n) {
    height: 10px;
}

.sound-wave .sound-bar:nth-child(17n) {
    height: 20px;
}

/* 增强动画效果 */


.audio-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.play-pause-btn {
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: opacity 0.2s;
}

.play-pause-btn:hover {
    opacity: 0.8;
}

.audio-time {
    font-size: 14px;
    color: #666;
    min-width: 40px;
    text-align: right;
}

.audio-title {
    font-size: 14px;
    color: #666;
}

/* 第1个波形条 - 默认动画 */
.sound-wave .sound-bar:nth-child(1),
.sound-wave .sound-bar:nth-child(10),
.sound-wave .sound-bar:nth-child(20),
.sound-wave .sound-bar:nth-child(29) {
    animation-name: bar-scale-sm;
    animation-duration: 0.5s;
}

/* 第2个波形条 */
.sound-wave .sound-bar:nth-child(2),
.sound-wave .sound-bar:nth-child(11),
.sound-wave .sound-bar:nth-child(21),
.sound-wave .sound-bar:nth-child(30) {
    animation-name: bar-scale-sm;
    animation-duration: 0.9s;
    animation-delay: 0.1s;
}

/* 第3个波形条 */
.sound-wave .sound-bar:nth-child(3),
.sound-wave .sound-bar:nth-child(12),
.sound-wave .sound-bar:nth-child(22) {
    animation-name: bar-scale-lg;
    animation-duration: 0.8s;
}

/* 第4个波形条 */
.sound-wave .sound-bar:nth-child(4),
.sound-wave .sound-bar:nth-child(13),
.sound-wave .sound-bar:nth-child(23) {
    animation-name: bar-scale-xl;
    animation-duration: 1.15s;
}

/* 第5个波形条 */
.sound-wave .sound-bar:nth-child(5),
.sound-wave .sound-bar:nth-child(14),
.sound-wave .sound-bar:nth-child(24) {
    animation-name: bar-scale-sm;
    animation-duration: 0.9s;
    animation-delay: 0.1s;
}

/* 第6个波形条 */
.sound-wave .sound-bar:nth-child(6),
.sound-wave .sound-bar:nth-child(15),
.sound-wave .sound-bar:nth-child(25) {
    animation-name: bar-scale-md;
    animation-duration: 0.85s;
}

/* 第7个波形条 */
.sound-wave .sound-bar:nth-child(7),
.sound-wave .sound-bar:nth-child(16),
.sound-wave .sound-bar:nth-child(26) {
    animation-name: bar-scale-sm;
    animation-duration: 0.9s;
    animation-delay: 0.1s;
}

/* 第8个波形条 */
.sound-wave .sound-bar:nth-child(8),
.sound-wave .sound-bar:nth-child(17),
.sound-wave .sound-bar:nth-child(27) {
    animation-name: bar-scale-xl;
    animation-duration: 1s;
}

/* 第9个波形条 */
.sound-wave .sound-bar:nth-child(9),
.sound-wave .sound-bar:nth-child(18),
.sound-wave .sound-bar:nth-child(28) {
    animation-name: bar-scale-sm;
    animation-duration: 0.9s;
    animation-delay: 0.1s;
}

/* 第19个波形条 - 参考第1个 */
.sound-wave .sound-bar:nth-child(19) {
    animation-name: bar-scale-sm;
    animation-duration: 0.5s;
}

.audio-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.play-pause-btn {
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: opacity 0.2s;
}

.play-pause-btn:hover {
    opacity: 0.8;
}

.audio-time {
    font-size: 14px;
    color: #666;
    min-width: 40px;
    text-align: right;
}

.audio-title {
    font-size: 14px;
    color: #666;
}

/* 新的卡片容器樣式 */
.qbi-card-container {
    width: 100%;
    overflow: hidden;
    cursor: grab;
    user-select: none;
    position: relative;
    margin: 10px 0;
}

.qbi-card-container:active {
    cursor: grabbing;
}

.qbi-card-wrapper {
    display: flex;
    gap: 15px;
    padding: 10px 0;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.qbi-card-wrapper::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.qbi-card-item {
    flex: 0 0 auto;
    min-width: 280px;
    max-width: 320px;
}

.qbi-card-item .card {
    height: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.qbi-card-item .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .qbi-card-item {
        min-width: 250px;
        max-width: 280px;
    }
}